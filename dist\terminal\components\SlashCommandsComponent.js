import inquirer from 'inquirer';
import chalk from 'chalk';
import fs from 'fs-extra';
import path from 'path';
import { StatusIndicator } from './ThinkingAnimation.js';
import { HeaderComponent } from './HeaderComponent.js';
import { MessageHistoryComponent } from './MessageHistoryComponent.js';
export class SlashCommandsComponent {
    configManager;
    state;
    messageHistoryComponent;
    constructor(configManager, state) {
        this.configManager = configManager;
        this.state = state;
        this.messageHistoryComponent = new MessageHistoryComponent({
            showTimestamps: true,
            showToolDetails: true,
            maxDisplayLength: 100
        });
    }
    getCommands() {
        return [
            {
                command: '/model',
                description: 'Switch to a different model',
                handler: this.handleModelCommand.bind(this)
            },
            {
                command: '/provider',
                description: 'Switch to a different provider (deepseek/ollama)',
                handler: this.handleProviderCommand.bind(this)
            },
            {
                command: '/session',
                description: 'Session management (new/clear/save)',
                handler: this.handleSessionCommand.bind(this)
            },
            {
                command: '/history',
                description: 'Show message history',
                handler: this.handleHistoryCommand.bind(this)
            },
            {
                command: '/system',
                description: 'Show system information',
                handler: this.handleSystemCommand.bind(this)
            },
            {
                command: '/debug',
                description: 'Toggle debug/tool visibility settings',
                handler: this.handleDebugCommand.bind(this)
            },
            {
                command: '/test',
                description: 'Test API connection and configuration',
                handler: this.handleTestCommand.bind(this)
            },
            {
                command: '/help',
                description: 'Show help message',
                handler: this.handleHelpCommand.bind(this)
            },
            {
                command: '/exit',
                description: 'Exit the CLI',
                handler: this.handleExitCommand.bind(this)
            }
        ];
    }
    async executeCommand(input) {
        const parts = input.trim().split(' ');
        const command = parts[0];
        const args = parts.slice(1);
        const slashCommand = this.getCommands().find(cmd => cmd.command === command);
        if (slashCommand) {
            try {
                await slashCommand.handler(args);
                return true;
            }
            catch (error) {
                StatusIndicator.error(`Command failed: ${error.message}`);
                return true;
            }
        }
        return false;
    }
    async handleModelCommand(args) {
        const config = this.configManager.getConfig();
        const currentProvider = config.providers[config.currentProvider];
        if (!currentProvider) {
            StatusIndicator.error('No provider configured');
            return;
        }
        if (args.length === 0) {
            // Show available models
            console.log(chalk.cyan('Available models:'));
            currentProvider.models.forEach((model) => {
                const indicator = model === config.currentModel ? chalk.green('●') : chalk.gray('○');
                console.log(`  ${indicator} ${model}`);
            });
            return;
        }
        const targetModel = args[0];
        if (!targetModel) {
            StatusIndicator.error('Please specify a model name');
            return;
        }
        if (!currentProvider.models.includes(targetModel)) {
            StatusIndicator.error(`Model '${targetModel}' not available for ${config.currentProvider}`);
            console.log(chalk.gray('Available models: ' + currentProvider.models.join(', ')));
            return;
        }
        await this.configManager.updateCurrentModel(targetModel);
        this.state.model = targetModel;
        StatusIndicator.success(`Switched to model: ${targetModel}`);
    }
    async handleProviderCommand(args) {
        const config = this.configManager.getConfig();
        if (args.length === 0) {
            // Show available providers
            console.log(chalk.cyan('Available providers:'));
            Object.keys(config.providers).forEach(provider => {
                const indicator = provider === config.currentProvider ? chalk.green('●') : chalk.gray('○');
                console.log(`  ${indicator} ${provider}`);
            });
            return;
        }
        const targetProvider = args[0];
        if (!['deepseek', 'ollama'].includes(targetProvider)) {
            StatusIndicator.error('Invalid provider. Available: deepseek, ollama');
            return;
        }
        if (!config.providers[targetProvider]) {
            StatusIndicator.error(`Provider '${targetProvider}' is not configured`);
            return;
        }
        await this.configManager.updateCurrentProvider(targetProvider);
        this.state.provider = targetProvider;
        this.state.model = config.providers[targetProvider].models[0];
        StatusIndicator.success(`Switched to provider: ${targetProvider}`);
    }
    async handleSessionCommand(args) {
        if (args.length === 0) {
            console.log(chalk.cyan('Session commands:'));
            console.log('  /session new    - Start a new session');
            console.log('  /session clear  - Clear current session');
            console.log('  /session save   - Save session to file');
            return;
        }
        const action = args[0];
        switch (action) {
            case 'new':
                await this.startNewSession();
                break;
            case 'clear':
                await this.clearSession();
                break;
            case 'save':
                await this.saveSession();
                break;
            default:
                StatusIndicator.error(`Unknown session action: ${action}`);
        }
    }
    async startNewSession() {
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: 'Start a new session? Current session will be cleared.',
                default: false
            }
        ]);
        if (confirm) {
            await this.configManager.clearHistory();
            this.state.messageHistory = [];
            this.state.currentSession = `session_${Date.now()}`;
            StatusIndicator.success('New session started');
        }
    }
    async clearSession() {
        const { confirm } = await inquirer.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: 'Clear current session? All messages will be lost.',
                default: false
            }
        ]);
        if (confirm) {
            await this.configManager.clearHistory();
            this.state.messageHistory = [];
            StatusIndicator.success('Session cleared');
        }
    }
    async saveSession() {
        if (this.state.messageHistory.length === 0) {
            StatusIndicator.warning('No messages to save');
            return;
        }
        const { filename } = await inquirer.prompt([
            {
                type: 'input',
                name: 'filename',
                message: 'Enter filename for session export:',
                default: `session_${new Date().toISOString().split('T')[0]}.json`,
                validate: (input) => {
                    if (!input.trim()) {
                        return 'Filename cannot be empty';
                    }
                    return true;
                }
            }
        ]);
        try {
            const sessionData = {
                sessionId: this.state.currentSession,
                provider: this.state.provider,
                model: this.state.model,
                timestamp: new Date().toISOString(),
                messageCount: this.state.messageHistory.length,
                messages: this.state.messageHistory
            };
            const filepath = path.resolve(filename);
            await fs.writeJSON(filepath, sessionData, { spaces: 2 });
            StatusIndicator.success(`Session saved to: ${filepath}`);
        }
        catch (error) {
            StatusIndicator.error(`Failed to save session: ${error.message}`);
        }
    }
    async handleHistoryCommand(args) {
        const limit = args.length > 0 ? parseInt(args[0] || '10') : 10;
        if (this.state.messageHistory.length === 0) {
            StatusIndicator.info('No message history');
            return;
        }
        // Update message history component with current state
        this.messageHistoryComponent.setMessages(this.state.messageHistory);
        // Check for additional options
        const option = args[1];
        if (option === '--compact' || option === '-c') {
            console.log(this.messageHistoryComponent.renderCompactHistory(limit));
        }
        else if (option === '--search' || option === '-s') {
            const query = args.slice(2).join(' ');
            if (!query) {
                StatusIndicator.error('Please provide a search query');
                return;
            }
            const searchResults = this.messageHistoryComponent.searchMessages(query);
            if (searchResults.length === 0) {
                StatusIndicator.info(`No messages found matching "${query}"`);
                return;
            }
            console.log(chalk.cyan(`\n🔍 Search Results for "${query}" (${searchResults.length} messages):\n`));
            this.messageHistoryComponent.setMessages(searchResults);
            console.log(this.messageHistoryComponent.renderHistory());
        }
        else if (option === '--stats' || option === '--statistics') {
            const stats = this.messageHistoryComponent.getStatistics();
            console.log(chalk.cyan('\n📊 Message History Statistics:\n'));
            console.log(chalk.white(`Total Messages: ${stats.totalMessages}`));
            console.log(chalk.blue(`User Messages: ${stats.userMessages}`));
            console.log(chalk.green(`Assistant Messages: ${stats.assistantMessages}`));
            console.log(chalk.yellow(`Tool Calls: ${stats.toolCalls}`));
            console.log(chalk.green(`Successful Tool Calls: ${stats.successfulToolCalls}`));
            console.log(chalk.red(`Failed Tool Calls: ${stats.failedToolCalls}`));
        }
        else if (option === '--export') {
            const filename = args[2] || `history_${Date.now()}.json`;
            try {
                const exportData = this.messageHistoryComponent.exportHistory();
                const filepath = path.resolve(filename);
                await fs.writeFile(filepath, exportData, 'utf8');
                StatusIndicator.success(`History exported to: ${filepath}`);
            }
            catch (error) {
                StatusIndicator.error(`Failed to export history: ${error.message}`);
            }
        }
        else {
            console.log(this.messageHistoryComponent.renderHistory(limit));
        }
        // Show usage help if no valid option
        if (args.length > 1 && option && !['--compact', '-c', '--search', '-s', '--stats', '--statistics', '--export'].includes(option)) {
            console.log(chalk.gray('\nUsage:'));
            console.log(chalk.gray('  /history [limit]           - Show recent messages'));
            console.log(chalk.gray('  /history [limit] --compact - Show compact view'));
            console.log(chalk.gray('  /history --search <query>  - Search messages'));
            console.log(chalk.gray('  /history --stats           - Show statistics'));
            console.log(chalk.gray('  /history --export [file]   - Export to JSON'));
        }
    }
    async handleSystemCommand(_args) {
        console.log(HeaderComponent.renderSystemInfo());
        const config = this.configManager.getConfig();
        console.log(chalk.cyan('\nConfiguration:'));
        console.log(chalk.gray(`  Config file: ${this.configManager.getConfigPath()}`));
        console.log(chalk.gray(`  Current provider: ${config.currentProvider}`));
        console.log(chalk.gray(`  Current model: ${config.currentModel}`));
        console.log(chalk.gray(`  Auto-approve: ${config.autoApprove ? 'enabled' : 'disabled'}`));
        console.log(chalk.gray(`  Max retries: ${config.maxRetries}`));
        console.log(chalk.gray(`  Working directory: ${config.workingDirectory}`));
        console.log(chalk.gray(`  Show tool details: ${config.showToolDetails ? 'enabled' : 'disabled'}`));
        console.log(chalk.gray(`  Show execution details: ${config.showToolExecutionDetails ? 'enabled' : 'disabled'}`));
    }
    async handleDebugCommand(args) {
        const config = this.configManager.getConfig();
        if (args.length === 0) {
            console.log(chalk.cyan('Debug/Tool Visibility Settings:'));
            console.log(chalk.gray(`  Tool details: ${config.showToolDetails ? 'enabled' : 'disabled'}`));
            console.log(chalk.gray(`  Execution details: ${config.showToolExecutionDetails ? 'enabled' : 'disabled'}`));
            console.log(chalk.gray('\nCommands:'));
            console.log(chalk.gray('  /debug tools          - Toggle tool details visibility'));
            console.log(chalk.gray('  /debug execution      - Toggle execution details visibility'));
            console.log(chalk.gray('  /debug enable         - Enable all debug output'));
            console.log(chalk.gray('  /debug disable        - Disable all debug output'));
            return;
        }
        const action = args[0];
        switch (action) {
            case 'tools':
                const newToolDetails = await this.configManager.toggleToolDetails();
                StatusIndicator.success(`Tool details ${newToolDetails ? 'enabled' : 'disabled'}`);
                break;
            case 'execution':
                const newExecutionDetails = await this.configManager.toggleToolExecutionDetails();
                StatusIndicator.success(`Execution details ${newExecutionDetails ? 'enabled' : 'disabled'}`);
                break;
            case 'enable':
                await this.configManager.setToolDetailsVisibility(true, true);
                StatusIndicator.success('All debug output enabled');
                break;
            case 'disable':
                await this.configManager.setToolDetailsVisibility(false, false);
                StatusIndicator.success('All debug output disabled');
                break;
            default:
                StatusIndicator.error(`Unknown debug action: ${action}`);
                console.log(chalk.gray('Available actions: tools, execution, enable, disable'));
        }
    }
    async handleTestCommand(_args) {
        const config = this.configManager.getConfig();
        const currentProvider = config.providers[config.currentProvider];
        if (!currentProvider) {
            StatusIndicator.error('No provider configured');
            return;
        }
        console.log(chalk.cyan('🔍 Testing API Connection...'));
        console.log(chalk.gray(`Provider: ${config.currentProvider}`));
        console.log(chalk.gray(`Model: ${config.currentModel}`));
        console.log(chalk.gray(`Base URL: ${currentProvider.baseUrl}`));
        try {
            // Import the provider dynamically
            if (config.currentProvider === 'deepseek') {
                const { DeepseekProvider } = await import('../../providers/DeepseekProvider.js');
                const provider = new DeepseekProvider(currentProvider);
                const testResult = await provider.testConnection();
                if (testResult.success) {
                    StatusIndicator.success(`Connection successful! Latency: ${testResult.latency}ms`);
                }
                else {
                    StatusIndicator.error(`Connection failed: ${testResult.error}`);
                    if (testResult.latency) {
                        console.log(chalk.gray(`Latency: ${testResult.latency}ms`));
                    }
                }
            }
            else if (config.currentProvider === 'ollama') {
                const { OllamaProvider } = await import('../../providers/OllamaProvider.js');
                const provider = new OllamaProvider(currentProvider);
                const isValid = await provider.validateConnection();
                if (isValid) {
                    StatusIndicator.success('Connection successful!');
                }
                else {
                    StatusIndicator.error('Connection failed');
                }
            }
        }
        catch (error) {
            StatusIndicator.error(`Test failed: ${error.message}`);
        }
    }
    async handleHelpCommand(_args) {
        console.log(HeaderComponent.renderHelp());
    }
    async handleExitCommand(_args) {
        console.log(chalk.cyan('👋 Goodbye!'));
        process.exit(0);
    }
    isSlashCommand(input) {
        return input.trim().startsWith('/');
    }
}
//# sourceMappingURL=SlashCommandsComponent.js.map