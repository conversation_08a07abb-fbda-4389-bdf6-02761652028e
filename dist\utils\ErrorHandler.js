import chalk from 'chalk';
export class ErrorHandler {
    retryConfig;
    constructor(retryConfig) {
        this.retryConfig = {
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffMultiplier: 2,
            retryableErrors: [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'ECONNABORTED',
                'ENETUNREACH',
                'ENETDOWN',
                'EHOSTUNREACH',
                'EHOSTDOWN',
                'EPIPE',
                'RATE_LIMIT',
                'NETWORK_ERROR',
                'TEMPORARY_ERROR'
            ],
            ...retryConfig
        };
    }
    async executeWithRetry(operation, context = 'Operation') {
        let lastError;
        for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
            try {
                return await operation();
            }
            catch (error) {
                lastError = error;
                if (!this.isRetryableError(error) || attempt === this.retryConfig.maxRetries) {
                    throw error;
                }
                const delay = this.calculateDelay(attempt);
                console.log(chalk.yellow(`⚠️  ${context} failed (attempt ${attempt}/${this.retryConfig.maxRetries}). Retrying in ${delay}ms...`));
                console.log(chalk.gray(`   Error: ${error.message}`));
                await this.sleep(delay);
            }
        }
        throw lastError;
    }
    isRetryableError(error) {
        const errorMessage = error.message.toLowerCase();
        const errorCode = error.code;
        // Check for specific error codes
        if (errorCode && this.retryConfig.retryableErrors.includes(errorCode)) {
            return true;
        }
        // Check for axios-specific errors
        if (error.isAxiosError) {
            const axiosError = error;
            // Check for network errors
            if (axiosError.code === 'ECONNABORTED' || axiosError.code === 'ETIMEDOUT') {
                return true;
            }
            // Check for HTTP status codes that should be retried
            if (axiosError.response) {
                const status = axiosError.response.status;
                if (status >= 500 || status === 429 || status === 408 || status === 502 || status === 503 || status === 504) {
                    return true;
                }
            }
        }
        // Check for rate limiting
        if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
            return true;
        }
        // Check for network-related errors
        if (errorMessage.includes('network') || errorMessage.includes('connection')) {
            return true;
        }
        // Check for timeout errors
        if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
            return true;
        }
        // Check for aborted requests
        if (errorMessage.includes('aborted') || errorMessage.includes('cancelled')) {
            return true;
        }
        // Check for DNS resolution errors
        if (errorMessage.includes('getaddrinfo') || errorMessage.includes('dns')) {
            return true;
        }
        return false;
    }
    calculateDelay(attempt) {
        const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1);
        return Math.min(delay, this.retryConfig.maxDelay);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    formatError(error, context) {
        const contextStr = context ? `[${context}] ` : '';
        return `${contextStr}${error.name}: ${error.message}`;
    }
    logError(error, context) {
        console.error(chalk.red('❌ Error:'), this.formatError(error, context));
        if (error.stack && process.env['DEBUG']) {
            console.error(chalk.gray(error.stack));
        }
    }
    logWarning(message, context) {
        const contextStr = context ? `[${context}] ` : '';
        console.warn(chalk.yellow('⚠️  Warning:'), `${contextStr}${message}`);
    }
    logInfo(message, context) {
        const contextStr = context ? `[${context}] ` : '';
        console.info(chalk.blue('ℹ️  Info:'), `${contextStr}${message}`);
    }
    logSuccess(message, context) {
        const contextStr = context ? `[${context}] ` : '';
        console.log(chalk.green('✅ Success:'), `${contextStr}${message}`);
    }
    createUserFriendlyError(error, suggestions) {
        let message = error.message;
        // Add user-friendly explanations for common errors
        if (error.message.includes('ENOTFOUND')) {
            message += '\n💡 This usually means there\'s a network connectivity issue or the server is unreachable.';
        }
        else if (error.message.includes('ECONNREFUSED')) {
            message += '\n💡 The connection was refused. The server might be down or the URL might be incorrect.';
        }
        else if (error.message.includes('aborted') || error.message.includes('ECONNABORTED')) {
            message += '\n💡 The request was aborted, likely due to a timeout or network issue. This is usually temporary.';
        }
        else if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
            message += '\n💡 The request timed out. The server might be slow or overloaded.';
        }
        else if (error.message.includes('rate limit')) {
            message += '\n💡 You\'ve hit the API rate limit. Please wait a moment before trying again.';
        }
        else if (error.message.includes('unauthorized') || error.message.includes('401')) {
            message += '\n💡 Authentication failed. Please check your API key.';
        }
        else if (error.message.includes('502') || error.message.includes('503') || error.message.includes('504')) {
            message += '\n💡 Server error. The API service might be temporarily unavailable.';
        }
        if (suggestions && suggestions.length > 0) {
            message += '\n\n🔧 Suggestions:';
            suggestions.forEach((suggestion, index) => {
                message += `\n   ${index + 1}. ${suggestion}`;
            });
        }
        const friendlyError = new Error(message);
        friendlyError.name = error.name;
        if (error.stack) {
            friendlyError.stack = error.stack;
        }
        return friendlyError;
    }
}
//# sourceMappingURL=ErrorHandler.js.map