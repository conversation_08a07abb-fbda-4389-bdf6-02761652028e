import axios from 'axios';
import { <PERSON>rrorHandler } from '../utils/ErrorHandler.js';
import { ShellTool } from '../tools/ShellTool.js';
import { getSystemPrompt } from '../prompts/SystemPrompt.js';
export class OllamaProvider {
    client;
    config;
    errorHandler;
    shellTool;
    constructor(config) {
        this.config = config;
        this.errorHandler = new ErrorHandler();
        this.shellTool = new ShellTool();
        this.client = axios.create({
            baseURL: config.baseUrl,
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 120000 // Ollama can be slower
        });
    }
    async chat(messages, model) {
        const selectedModel = model || this.config.models[0];
        // Separate API call from tool execution to prevent tool re-execution on retries
        const apiResponse = await this.errorHandler.executeWithRetry(async () => {
            // Add system prompt if not present
            const formattedMessages = this.formatMessages(messages);
            if (formattedMessages.length === 0 || formattedMessages[0].role !== 'system') {
                formattedMessages.unshift({
                    role: 'system',
                    content: getSystemPrompt()
                });
            }
            // Ollama uses a different API format
            const response = await this.client.post('/api/chat', {
                model: selectedModel,
                messages: formattedMessages,
                tools: this.getAvailableTools(),
                stream: false,
                options: {
                    temperature: 0.7,
                    num_predict: 4000
                }
            });
            const assistantMessage = response.data.message;
            if (!assistantMessage) {
                throw new Error('No response from Ollama API');
            }
            return assistantMessage;
        }, 'Ollama API call');
        // Handle tool calls if present (outside retry logic to prevent re-execution)
        if (apiResponse.tool_calls && apiResponse.tool_calls.length > 0) {
            const toolResults = await this.executeToolCalls(apiResponse.tool_calls);
            return {
                id: this.generateId(),
                role: 'assistant',
                content: apiResponse.content || '',
                timestamp: new Date(),
                toolCalls: apiResponse.tool_calls.map((tc) => ({
                    id: tc.id || this.generateId(),
                    name: tc.function?.name || tc.name,
                    arguments: typeof tc.function?.arguments === 'string' ?
                        JSON.parse(tc.function.arguments) :
                        tc.arguments || tc.function?.arguments
                })),
                toolResults
            };
        }
        return {
            id: this.generateId(),
            role: 'assistant',
            content: apiResponse.content || '',
            timestamp: new Date()
        };
    }
    formatMessages(messages) {
        return messages.map(msg => {
            const formatted = {
                role: msg.role,
                content: msg.content
            };
            // Add tool calls if present (adapt to Ollama format)
            if (msg.toolCalls && msg.toolCalls.length > 0) {
                formatted.tool_calls = msg.toolCalls.map(tc => ({
                    id: tc.id,
                    function: {
                        name: tc.name,
                        arguments: tc.arguments
                    }
                }));
            }
            return formatted;
        });
    }
    getAvailableTools() {
        const shellToolDef = ShellTool.getToolDefinition();
        return [
            {
                type: 'function',
                function: {
                    name: shellToolDef.name,
                    description: shellToolDef.description,
                    parameters: shellToolDef.parameters
                }
            }
        ];
    }
    async executeToolCalls(toolCalls) {
        const results = [];
        for (const toolCall of toolCalls) {
            const startTime = Date.now();
            try {
                let result;
                const functionName = toolCall.function?.name || toolCall.name;
                const functionArgs = toolCall.function?.arguments || toolCall.arguments;
                if (functionName === 'execute_shell_command') {
                    const args = typeof functionArgs === 'string' ?
                        JSON.parse(functionArgs) : functionArgs;
                    result = await this.shellTool.execute({
                        command: args.command,
                        args: args.args,
                        cwd: args.cwd,
                        timeout: args.timeout,
                        requireApproval: args.requireApproval
                    });
                }
                else {
                    throw new Error(`Unknown tool: ${functionName}`);
                }
                results.push({
                    id: toolCall.id || this.generateId(),
                    name: functionName,
                    result,
                    success: true,
                    executionTime: Date.now() - startTime
                });
            }
            catch (error) {
                results.push({
                    id: toolCall.id || this.generateId(),
                    name: toolCall.function?.name || toolCall.name,
                    result: null,
                    success: false,
                    error: error.message,
                    executionTime: Date.now() - startTime
                });
            }
        }
        return results;
    }
    async getAvailableModels() {
        try {
            const response = await this.client.get('/api/tags');
            return response.data.models?.map((model) => model.name) || [];
        }
        catch (error) {
            this.errorHandler.logWarning('Could not fetch models from Ollama API, using configured models');
            return this.config.models;
        }
    }
    async validateConnection() {
        try {
            await this.client.get('/api/tags');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async pullModel(modelName) {
        return this.errorHandler.executeWithRetry(async () => {
            await this.client.post('/api/pull', {
                name: modelName
            });
        }, `Pulling model ${modelName}`);
    }
    async deleteModel(modelName) {
        return this.errorHandler.executeWithRetry(async () => {
            await this.client.delete('/api/delete', {
                data: { name: modelName }
            });
        }, `Deleting model ${modelName}`);
    }
    generateId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    updateConfig(config) {
        this.config = config;
        this.client.defaults.baseURL = config.baseUrl;
    }
    getConfig() {
        return { ...this.config };
    }
    async getModelInfo(modelName) {
        try {
            const response = await this.client.post('/api/show', {
                name: modelName
            });
            return response.data;
        }
        catch (error) {
            throw new Error(`Could not get info for model ${modelName}: ${error.message}`);
        }
    }
}
//# sourceMappingURL=OllamaProvider.js.map