import chalk from 'chalk';
import { RetryConfig } from '../types/index.js';

export class ErrorHandler {
  private retryConfig: RetryConfig;

  constructor(retryConfig?: Partial<RetryConfig>) {
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      retryableErrors: [
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ETIMEDOUT',
        'RATE_LIMIT',
        'NETWORK_ERROR',
        'TEMPORARY_ERROR'
      ],
      ...retryConfig
    };
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string = 'Operation'
  ): Promise<T> {
    let lastError: Error | undefined;
    
    for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (!this.isRetryableError(error as Error) || attempt === this.retryConfig.maxRetries) {
          throw error;
        }
        
        const delay = this.calculateDelay(attempt);
        console.log(
          chalk.yellow(
            `⚠️  ${context} failed (attempt ${attempt}/${this.retryConfig.maxRetries}). Retrying in ${delay}ms...`
          )
        );
        console.log(chalk.gray(`   Error: ${(error as Error).message}`));
        
        await this.sleep(delay);
      }
    }
    
    throw lastError;
  }

  private isRetryableError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase();
    const errorCode = (error as any).code;
    
    // Check for specific error codes
    if (errorCode && this.retryConfig.retryableErrors.includes(errorCode)) {
      return true;
    }
    
    // Check for rate limiting
    if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
      return true;
    }
    
    // Check for network-related errors
    if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return true;
    }
    
    // Check for timeout errors
    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return true;
    }
    
    return false;
  }

  private calculateDelay(attempt: number): number {
    const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt - 1);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  formatError(error: Error, context?: string): string {
    const contextStr = context ? `[${context}] ` : '';
    return `${contextStr}${error.name}: ${error.message}`;
  }

  logError(error: Error, context?: string): void {
    console.error(chalk.red('❌ Error:'), this.formatError(error, context));
    
    if (error.stack && process.env['DEBUG']) {
      console.error(chalk.gray(error.stack));
    }
  }

  logWarning(message: string, context?: string): void {
    const contextStr = context ? `[${context}] ` : '';
    console.warn(chalk.yellow('⚠️  Warning:'), `${contextStr}${message}`);
  }

  logInfo(message: string, context?: string): void {
    const contextStr = context ? `[${context}] ` : '';
    console.info(chalk.blue('ℹ️  Info:'), `${contextStr}${message}`);
  }

  logSuccess(message: string, context?: string): void {
    const contextStr = context ? `[${context}] ` : '';
    console.log(chalk.green('✅ Success:'), `${contextStr}${message}`);
  }

  createUserFriendlyError(error: Error, suggestions?: string[]): Error {
    let message = error.message;
    
    // Add user-friendly explanations for common errors
    if (error.message.includes('ENOTFOUND')) {
      message += '\n💡 This usually means there\'s a network connectivity issue or the server is unreachable.';
    } else if (error.message.includes('ECONNREFUSED')) {
      message += '\n💡 The connection was refused. The server might be down or the URL might be incorrect.';
    } else if (error.message.includes('rate limit')) {
      message += '\n💡 You\'ve hit the API rate limit. Please wait a moment before trying again.';
    } else if (error.message.includes('unauthorized') || error.message.includes('401')) {
      message += '\n💡 Authentication failed. Please check your API key.';
    }
    
    if (suggestions && suggestions.length > 0) {
      message += '\n\n🔧 Suggestions:';
      suggestions.forEach((suggestion, index) => {
        message += `\n   ${index + 1}. ${suggestion}`;
      });
    }
    
    const friendlyError = new Error(message);
    friendlyError.name = error.name;
    if (error.stack) {
      friendlyError.stack = error.stack;
    }
    
    return friendlyError;
  }
}
