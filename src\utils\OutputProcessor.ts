import chalk from 'chalk';
import stripAnsi from 'strip-ansi';
import { ShellR<PERSON>ult, ToolResult, ChatMessage } from '../types/index.js';

export interface ProcessingOptions {
  maxLength?: number;
  highlightKeywords?: string[];
  formatJson?: boolean;
  showLineNumbers?: boolean;
  truncateLines?: number;
  colorScheme?: 'default' | 'minimal' | 'verbose';
}

export interface ProcessedOutput {
  formatted: string;
  summary: string;
  metadata: {
    lineCount: number;
    characterCount: number;
    hasErrors: boolean;
    detectedFormat: string;
    keywords: string[];
  };
}

export class OutputProcessor {
  private static readonly DEFAULT_OPTIONS: ProcessingOptions = {
    maxLength: 5000,
    highlightKeywords: ['error', 'warning', 'success', 'failed', 'completed'],
    formatJson: true,
    showLineNumbers: false,
    truncateLines: 50,
    colorScheme: 'default'
  };

  static processShellOutput(result: ShellResult, options: ProcessingOptions = {}): ProcessedOutput {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const lines: string[] = [];
    
    // Process stdout
    if (result.stdout.trim()) {
      const processedStdout = this.processText(result.stdout, opts, 'stdout');
      lines.push(chalk.green('📤 Output:'));
      lines.push(processedStdout.formatted);
    }

    // Process stderr
    if (result.stderr.trim()) {
      const processedStderr = this.processText(result.stderr, opts, 'stderr');
      lines.push(chalk.red('📥 Errors:'));
      lines.push(processedStderr.formatted);
    }

    // Add execution metadata
    lines.push(this.formatExecutionMetadata(result));

    const formatted = lines.join('\n');
    const summary = this.generateShellSummary(result);
    
    return {
      formatted,
      summary,
      metadata: {
        lineCount: result.stdout.split('\n').length + result.stderr.split('\n').length,
        characterCount: result.stdout.length + result.stderr.length,
        hasErrors: !result.success || result.stderr.trim().length > 0,
        detectedFormat: this.detectFormat(result.stdout + result.stderr),
        keywords: this.extractKeywords(result.stdout + result.stderr, opts.highlightKeywords || [])
      }
    };
  }

  static processToolOutput(result: ToolResult, options: ProcessingOptions = {}): ProcessedOutput {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const lines: string[] = [];

    // Tool header
    lines.push(chalk.magenta(`🛠️  ${result.name} (${result.executionTime}ms)`));
    
    if (result.success) {
      const processedResult = this.processText(
        typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2),
        opts,
        'result'
      );
      lines.push(chalk.green('✅ Success:'));
      lines.push(processedResult.formatted);
    } else {
      lines.push(chalk.red('❌ Failed:'));
      lines.push(chalk.red(`   ${result.error || 'Unknown error'}`));
    }

    const formatted = lines.join('\n');
    const summary = this.generateToolSummary(result);

    return {
      formatted,
      summary,
      metadata: {
        lineCount: 1,
        characterCount: result.result ? String(result.result).length : 0,
        hasErrors: !result.success,
        detectedFormat: this.detectFormat(String(result.result || '')),
        keywords: this.extractKeywords(String(result.result || ''), opts.highlightKeywords || [])
      }
    };
  }

  static processAssistantMessage(message: ChatMessage, options: ProcessingOptions = {}): ProcessedOutput {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const lines: string[] = [];

    // Message content
    if (message.content) {
      const processedContent = this.processText(message.content, opts, 'message');
      lines.push(chalk.green('🤖 Assistant:'));
      lines.push(processedContent.formatted);
    }

    // Tool calls - show simplified version
    if (message.toolCalls && message.toolCalls.length > 0) {
      lines.push(chalk.yellow('\n🛠️  Tool Calls:'));
      message.toolCalls.forEach(toolCall => {
        lines.push(chalk.yellow(`   • ${toolCall.name}`));
        // Only show command for shell tools, hide other arguments
        if (toolCall.name === 'execute_shell_command' && toolCall.arguments['command']) {
          lines.push(chalk.gray(`        ${toolCall.arguments['command']}`));
        }
      });
    }

    // Tool results - show formatted output
    if (message.toolResults && message.toolResults.length > 0) {
      lines.push(chalk.magenta('\n📋 Tool Results:'));
      message.toolResults.forEach(result => {
        if (result.name === 'execute_shell_command') {
          // Special handling for shell commands
          const shellResult = result.result as any;
          lines.push(chalk.magenta(`   🛠️  ${result.name} (${result.executionTime}ms)`));

          if (result.success) {
            lines.push(chalk.green('   ✅ Success:'));
            // Don't show the raw JSON, just show the formatted output below
          } else {
            lines.push(chalk.red('   ❌ Failed:'));
            lines.push(chalk.red(`      Error: ${result.error || 'Unknown error'}`));
          }

          // Show formatted output
          if (shellResult && shellResult.stdout) {
            lines.push(chalk.cyan('\n📤 Output:'));
            const outputLines = shellResult.stdout.split('\n');
            outputLines.forEach((line: string) => {
              if (line.trim()) {
                lines.push(chalk.white(`   ${line}`));
              }
            });
          }

          // Show execution details
          lines.push(chalk.blue('📊 Execution Details:'));
          lines.push(chalk.gray(`   Command: ${shellResult.command}`));
          lines.push(chalk.gray(`   Exit Code: ${shellResult.exitCode}`));
          lines.push(chalk.gray(`   Duration: ${shellResult.executionTime}ms`));
          lines.push(chalk.gray(`   Status: ${shellResult.success ? '✅ Success' : '❌ Failed'}`));
        } else {
          // Default tool result processing
          const processed = this.processToolOutput(result, opts);
          lines.push(processed.formatted.split('\n').map(line => `   ${line}`).join('\n'));
        }
      });
    }

    const formatted = lines.join('\n');
    const summary = this.generateMessageSummary(message);

    return {
      formatted,
      summary,
      metadata: {
        lineCount: message.content.split('\n').length,
        characterCount: message.content.length,
        hasErrors: message.toolResults?.some(r => !r.success) || false,
        detectedFormat: this.detectFormat(message.content),
        keywords: this.extractKeywords(message.content, opts.highlightKeywords || [])
      }
    };
  }

  private static processText(text: string, options: ProcessingOptions, type: string): ProcessedOutput {
    let processed = text;
    const lines = text.split('\n');

    // Truncate if too long
    if (options.maxLength && processed.length > options.maxLength) {
      processed = processed.substring(0, options.maxLength) + '\n... (truncated)';
    }

    // Truncate lines if too many
    if (options.truncateLines && lines.length > options.truncateLines) {
      const truncatedLines = lines.slice(0, options.truncateLines);
      truncatedLines.push(`... (${lines.length - options.truncateLines} more lines)`);
      processed = truncatedLines.join('\n');
    }

    // Format JSON if detected and enabled
    if (options.formatJson && this.isJson(processed)) {
      try {
        const parsed = JSON.parse(processed);
        processed = JSON.stringify(parsed, null, 2);
      } catch {
        // Keep original if parsing fails
      }
    }

    // Apply syntax highlighting
    processed = this.applySyntaxHighlighting(processed, type, options);

    // Add line numbers if requested
    if (options.showLineNumbers) {
      processed = this.addLineNumbers(processed);
    }

    // Highlight keywords
    if (options.highlightKeywords && options.highlightKeywords.length > 0) {
      processed = this.highlightKeywords(processed, options.highlightKeywords);
    }

    // Apply color scheme
    processed = this.applyColorScheme(processed, options.colorScheme || 'default', type);

    return {
      formatted: processed,
      summary: this.generateTextSummary(text),
      metadata: {
        lineCount: lines.length,
        characterCount: text.length,
        hasErrors: this.containsErrors(text),
        detectedFormat: this.detectFormat(text),
        keywords: this.extractKeywords(text, options.highlightKeywords || [])
      }
    };
  }

  private static applySyntaxHighlighting(text: string, type: string, _options: ProcessingOptions): string {
    const lines = text.split('\n');
    
    return lines.map(line => {
      // Indent content
      const indentedLine = `   ${line}`;
      
      // Apply type-specific coloring
      switch (type) {
        case 'stdout':
          return chalk.white(indentedLine);
        case 'stderr':
          return chalk.red(indentedLine);
        case 'json':
          return this.highlightJson(indentedLine);
        case 'result':
          return chalk.cyan(indentedLine);
        case 'message':
          return chalk.white(indentedLine);
        default:
          return chalk.gray(indentedLine);
      }
    }).join('\n');
  }

  private static highlightJson(text: string): string {
    return text
      .replace(/"([^"]+)":/g, chalk.blue('"$1"') + chalk.white(':'))
      .replace(/: "([^"]+)"/g, ': ' + chalk.green('"$1"'))
      .replace(/: (\d+)/g, ': ' + chalk.yellow('$1'))
      .replace(/: (true|false)/g, ': ' + chalk.magenta('$1'))
      .replace(/: null/g, ': ' + chalk.gray('null'));
  }

  private static addLineNumbers(text: string): string {
    const lines = text.split('\n');
    const maxLineNumWidth = String(lines.length).length;
    
    return lines.map((line, index) => {
      const lineNum = String(index + 1).padStart(maxLineNumWidth, ' ');
      return chalk.gray(`${lineNum} │ `) + line;
    }).join('\n');
  }

  private static highlightKeywords(text: string, keywords: string[]): string {
    let highlighted = text;
    
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      highlighted = highlighted.replace(regex, (match) => {
        if (match.toLowerCase().includes('error') || match.toLowerCase().includes('fail')) {
          return chalk.red.bold(match);
        } else if (match.toLowerCase().includes('warn')) {
          return chalk.yellow.bold(match);
        } else if (match.toLowerCase().includes('success') || match.toLowerCase().includes('complete')) {
          return chalk.green.bold(match);
        } else {
          return chalk.cyan.bold(match);
        }
      });
    });
    
    return highlighted;
  }

  private static applyColorScheme(text: string, scheme: string, _type: string): string {
    switch (scheme) {
      case 'minimal':
        return stripAnsi(text);
      case 'verbose':
        // Keep all colors and add more emphasis
        return text;
      case 'default':
      default:
        return text;
    }
  }

  private static formatExecutionMetadata(result: ShellResult): string {
    const lines: string[] = [];
    
    lines.push(chalk.blue('📊 Execution Details:'));
    lines.push(chalk.gray(`   Command: ${result.command}`));
    lines.push(chalk.gray(`   Exit Code: ${result.exitCode}`));
    lines.push(chalk.gray(`   Duration: ${result.executionTime}ms`));
    lines.push(chalk.gray(`   Status: ${result.success ? '✅ Success' : '❌ Failed'}`));
    
    return lines.join('\n');
  }

  private static isJson(text: string): boolean {
    try {
      JSON.parse(text.trim());
      return true;
    } catch {
      return false;
    }
  }

  private static detectFormat(text: string): string {
    if (this.isJson(text)) return 'json';
    if (text.includes('<?xml')) return 'xml';
    if (text.includes('<!DOCTYPE html') || text.includes('<html')) return 'html';
    if (text.includes('```')) return 'markdown';
    if (text.match(/^\s*[\w-]+:\s/m)) return 'yaml';
    return 'text';
  }

  private static containsErrors(text: string): boolean {
    const errorPatterns = [
      /error/i,
      /exception/i,
      /failed/i,
      /fatal/i,
      /critical/i,
      /\[ERROR\]/i,
      /\[FATAL\]/i
    ];
    
    return errorPatterns.some(pattern => pattern.test(text));
  }

  private static extractKeywords(text: string, keywords: string[]): string[] {
    const found: string[] = [];
    const lowerText = text.toLowerCase();
    
    keywords.forEach(keyword => {
      if (lowerText.includes(keyword.toLowerCase())) {
        found.push(keyword);
      }
    });
    
    return [...new Set(found)];
  }

  private static generateShellSummary(result: ShellResult): string {
    const parts: string[] = [];
    
    if (result.success) {
      parts.push('✅ Command executed successfully');
    } else {
      parts.push('❌ Command failed');
    }
    
    if (result.stdout.trim()) {
      parts.push(`${result.stdout.split('\n').length} lines of output`);
    }
    
    if (result.stderr.trim()) {
      parts.push(`${result.stderr.split('\n').length} lines of errors`);
    }
    
    parts.push(`${result.executionTime}ms execution time`);
    
    return parts.join(', ');
  }

  private static generateToolSummary(result: ToolResult): string {
    const status = result.success ? '✅ Success' : '❌ Failed';
    return `${result.name}: ${status} (${result.executionTime}ms)`;
  }

  private static generateMessageSummary(message: ChatMessage): string {
    const parts: string[] = [];
    
    parts.push(`${message.content.length} characters`);
    
    if (message.toolCalls && message.toolCalls.length > 0) {
      parts.push(`${message.toolCalls.length} tool calls`);
    }
    
    if (message.toolResults && message.toolResults.length > 0) {
      const successful = message.toolResults.filter(r => r.success).length;
      parts.push(`${successful}/${message.toolResults.length} tools succeeded`);
    }
    
    return parts.join(', ');
  }

  private static generateTextSummary(text: string): string {
    const lines = text.split('\n').length;
    const chars = text.length;
    const words = text.split(/\s+/).length;
    
    return `${lines} lines, ${words} words, ${chars} characters`;
  }

  // Public utility methods
  static stripColors(text: string): string {
    return stripAnsi(text);
  }

  static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}
