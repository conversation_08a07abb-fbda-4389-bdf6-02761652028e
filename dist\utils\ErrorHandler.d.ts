import { RetryConfig } from '../types/index.js';
export declare class ErrorHandler {
    private retryConfig;
    constructor(retryConfig?: Partial<RetryConfig>);
    executeWithRetry<T>(operation: () => Promise<T>, context?: string): Promise<T>;
    private isRetryableError;
    private calculateDelay;
    private sleep;
    formatError(error: Error, context?: string): string;
    logError(error: Error, context?: string): void;
    logWarning(message: string, context?: string): void;
    logInfo(message: string, context?: string): void;
    logSuccess(message: string, context?: string): void;
    createUserFriendlyError(error: Error, suggestions?: string[]): Error;
}
//# sourceMappingURL=ErrorHandler.d.ts.map