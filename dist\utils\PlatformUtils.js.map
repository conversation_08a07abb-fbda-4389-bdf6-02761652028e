{"version": 3, "file": "PlatformUtils.js", "sourceRoot": "", "sources": ["../../src/utils/PlatformUtils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,CAAC;AAwBpB,MAAM,OAAO,aAAa;IAChB,MAAM,CAAC,QAAQ,CAAW;IAC1B,MAAM,CAAC,UAAU,CAAiB;IAE1C,MAAM,CAAC,cAAc;QACnB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QAED,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC/B,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,OAAO;gBACV,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;gBAC1B,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;gBACxB,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;gBACxB,MAAM;YACR;gBACE,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,iBAAiB;QACtB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,SAAS;gBACZ,IAAI,CAAC,UAAU,GAAG;oBAChB,SAAS,EAAE,KAAK;oBAChB,iBAAiB,EAAE,QAAQ;oBAC3B,eAAe,EAAE,MAAM;oBACvB,SAAS,EAAE,QAAQ;oBACnB,aAAa,EAAE,UAAU;oBACzB,WAAW,EAAE,oBAAoB;oBACjC,UAAU,EAAE,YAAY;oBACxB,WAAW,EAAE,aAAa;oBAC1B,UAAU,EAAE,eAAe;oBAC3B,eAAe,EAAE,IAAI;oBACrB,eAAe,EAAE,UAAU;oBAC3B,QAAQ,EAAE,MAAM;oBAChB,QAAQ,EAAE,MAAM;oBAChB,UAAU,EAAE,KAAK;oBACjB,gBAAgB,EAAE,IAAI;oBACtB,eAAe,EAAE,IAAI;oBACrB,aAAa,EAAE,IAAI;iBACpB,CAAC;gBACF,MAAM;YAER,KAAK,OAAO,CAAC;YACb,KAAK,OAAO;gBACV,IAAI,CAAC,UAAU,GAAG;oBAChB,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,QAAQ;oBAC3B,eAAe,EAAE,KAAK;oBACtB,SAAS,EAAE,cAAc;oBACzB,aAAa,EAAE,QAAQ;oBACvB,WAAW,EAAE,eAAe;oBAC5B,UAAU,EAAE,UAAU;oBACtB,WAAW,EAAE,aAAa;oBAC1B,UAAU,EAAE,SAAS;oBACrB,eAAe,EAAE,UAAU;oBAC3B,eAAe,EAAE,QAAQ;oBACzB,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,IAAI;oBAChB,gBAAgB,EAAE,KAAK;oBACvB,eAAe,EAAE,IAAI;oBACrB,aAAa,EAAE,GAAG;iBACnB,CAAC;gBACF,MAAM;YAER;gBACE,gCAAgC;gBAChC,IAAI,CAAC,UAAU,GAAG;oBAChB,SAAS,EAAE,IAAI;oBACf,iBAAiB,EAAE,QAAQ;oBAC3B,eAAe,EAAE,KAAK;oBACtB,SAAS,EAAE,cAAc;oBACzB,aAAa,EAAE,QAAQ;oBACvB,WAAW,EAAE,eAAe;oBAC5B,UAAU,EAAE,UAAU;oBACtB,WAAW,EAAE,aAAa;oBAC1B,UAAU,EAAE,SAAS;oBACrB,eAAe,EAAE,UAAU;oBAC3B,eAAe,EAAE,QAAQ;oBACzB,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,IAAI;oBAChB,gBAAgB,EAAE,KAAK;oBACvB,eAAe,EAAE,IAAI;oBACrB,aAAa,EAAE,GAAG;iBACnB,CAAC;QACN,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,MAAc,EAAE,IAAe;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvC,QAAQ,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7B,KAAK,YAAY,CAAC;YAClB,KAAK,YAAY,CAAC;YAClB,KAAK,IAAI,CAAC;YACV,KAAK,KAAK;gBACR,4DAA4D;gBAC5D,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;oBACrB,0DAA0D;oBAC1D,OAAO,QAAQ,CAAC,iBAAiB,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,2BAA2B;oBAC3B,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAC9B,GAAG,QAAQ,CAAC,iBAAiB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBACnD,QAAQ,CAAC,iBAAiB,CAAC;gBAC/B,CAAC;YAEH,KAAK,WAAW,CAAC;YACjB,KAAK,KAAK,CAAC;YACX,KAAK,MAAM;gBACT,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC9B,GAAG,QAAQ,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBACjD,QAAQ,CAAC,eAAe,CAAC;YAE7B,KAAK,YAAY,CAAC;YAClB,KAAK,cAAc;gBACjB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAC3B,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAC9B,GAAG,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBAC3C,QAAQ,CAAC,SAAS,CAAC;gBACvB,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAC9B,GAAG,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;wBAC7C,QAAQ,CAAC,SAAS,CAAC;gBACvB,CAAC;YAEH,KAAK,gBAAgB,CAAC;YACtB,KAAK,IAAI,CAAC;YACV,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC,aAAa,CAAC;YAEhC,KAAK,cAAc;gBACjB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,OAAO,GAAG,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACrD,CAAC;gBACD,OAAO,QAAQ,CAAC,aAAa,CAAC;YAEhC,KAAK,aAAa,CAAC;YACnB,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,UAAU,CAAC;YAE7B,KAAK,cAAc,CAAC;YACpB,KAAK,SAAS;gBACZ,OAAO,QAAQ,CAAC,WAAW,CAAC;YAE9B,KAAK,aAAa,CAAC;YACnB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS;gBACZ,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACtC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;wBAC3B,OAAO,GAAG,QAAQ,CAAC,UAAU,KAAK,OAAO,KAAK,KAAK,EAAE,CAAC;oBACxD,CAAC;yBAAM,CAAC;wBACN,OAAO,GAAG,QAAQ,CAAC,UAAU,KAAK,OAAO,KAAK,KAAK,EAAE,CAAC;oBACxD,CAAC;gBACH,CAAC;gBACD,OAAO,QAAQ,CAAC,UAAU,CAAC;YAE7B,KAAK,mBAAmB,CAAC;YACzB,KAAK,KAAK,CAAC;YACX,KAAK,IAAI;gBACP,OAAO,QAAQ,CAAC,gBAAgB,CAAC;YAEnC;gBACE,sDAAsD;gBACtD,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,eAAe;QACpB,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE;YAC/B,QAAQ,EAAE,IAAI,CAAC,iBAAiB,EAAE;SACnC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,SAAS;QACd,OAAO,IAAI,CAAC,cAAc,EAAE,KAAK,SAAS,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,MAAM;QACX,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACvC,OAAO,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,OAAO,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,IAAY;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;CACF"}