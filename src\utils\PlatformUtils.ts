import os from 'os';

export type Platform = 'windows' | 'macos' | 'linux' | 'unknown';

export interface CommandMapping {
  listFiles: string;
  listFilesDetailed: string;
  showFileContent: string;
  findFiles: string;
  listProcesses: string;
  findProcess: string;
  systemInfo: string;
  networkInfo: string;
  searchText: string;
  createDirectory: string;
  removeDirectory: string;
  copyFile: string;
  moveFile: string;
  deleteFile: string;
  currentDirectory: string;
  changeDirectory: string;
  pathSeparator: string;
}

export class PlatformUtils {
  private static platform: Platform;
  private static commandMap: CommandMapping;

  static detectPlatform(): Platform {
    if (this.platform) {
      return this.platform;
    }

    const platform = os.platform();
    switch (platform) {
      case 'win32':
        this.platform = 'windows';
        break;
      case 'darwin':
        this.platform = 'macos';
        break;
      case 'linux':
        this.platform = 'linux';
        break;
      default:
        this.platform = 'unknown';
    }

    return this.platform;
  }

  static getCommandMapping(): CommandMapping {
    if (this.commandMap) {
      return this.commandMap;
    }

    const platform = this.detectPlatform();
    
    switch (platform) {
      case 'windows':
        this.commandMap = {
          listFiles: 'dir',
          listFilesDetailed: 'dir /a',
          showFileContent: 'type',
          findFiles: 'dir /s',
          listProcesses: 'tasklist',
          findProcess: 'tasklist | findstr',
          systemInfo: 'systeminfo',
          networkInfo: 'netstat -an',
          searchText: 'findstr /s /i',
          createDirectory: 'md',
          removeDirectory: 'rd /s /q',
          copyFile: 'copy',
          moveFile: 'move',
          deleteFile: 'del',
          currentDirectory: 'cd',
          changeDirectory: 'cd',
          pathSeparator: '\\'
        };
        break;
      
      case 'macos':
      case 'linux':
        this.commandMap = {
          listFiles: 'ls',
          listFilesDetailed: 'ls -la',
          showFileContent: 'cat',
          findFiles: 'find . -name',
          listProcesses: 'ps aux',
          findProcess: 'ps aux | grep',
          systemInfo: 'uname -a',
          networkInfo: 'netstat -an',
          searchText: 'grep -r',
          createDirectory: 'mkdir -p',
          removeDirectory: 'rm -rf',
          copyFile: 'cp',
          moveFile: 'mv',
          deleteFile: 'rm',
          currentDirectory: 'pwd',
          changeDirectory: 'cd',
          pathSeparator: '/'
        };
        break;
      
      default:
        // Default to Unix-like commands
        this.commandMap = {
          listFiles: 'ls',
          listFilesDetailed: 'ls -la',
          showFileContent: 'cat',
          findFiles: 'find . -name',
          listProcesses: 'ps aux',
          findProcess: 'ps aux | grep',
          systemInfo: 'uname -a',
          networkInfo: 'netstat -an',
          searchText: 'grep -r',
          createDirectory: 'mkdir -p',
          removeDirectory: 'rm -rf',
          copyFile: 'cp',
          moveFile: 'mv',
          deleteFile: 'rm',
          currentDirectory: 'pwd',
          changeDirectory: 'cd',
          pathSeparator: '/'
        };
    }

    return this.commandMap;
  }

  static translateCommand(intent: string, args?: string[]): string {
    const commands = this.getCommandMapping();
    const platform = this.detectPlatform();

    switch (intent.toLowerCase()) {
      case 'list_files':
      case 'list files':
      case 'ls':
      case 'dir':
        // For ls command, translate to appropriate platform command
        if (this.isWindows()) {
          // Windows: ignore Unix-style flags and use Windows syntax
          return commands.listFilesDetailed;
        } else {
          // Unix: preserve arguments
          return args && args.length > 0 ?
            `${commands.listFilesDetailed} ${args.join(' ')}` :
            commands.listFilesDetailed;
        }

      case 'show_file':
      case 'cat':
      case 'type':
        return args && args.length > 0 ? 
          `${commands.showFileContent} ${args.join(' ')}` : 
          commands.showFileContent;

      case 'find_files':
      case 'search_files':
        if (platform === 'windows') {
          return args && args.length > 0 ? 
            `${commands.findFiles} ${args.join(' ')}` : 
            commands.findFiles;
        } else {
          return args && args.length > 0 ? 
            `${commands.findFiles} "${args.join(' ')}"` : 
            commands.findFiles;
        }

      case 'list_processes':
      case 'ps':
      case 'tasklist':
        return commands.listProcesses;

      case 'find_process':
        if (args && args.length > 0) {
          return `${commands.findProcess} ${args.join(' ')}`;
        }
        return commands.listProcesses;

      case 'system_info':
      case 'sysinfo':
        return commands.systemInfo;

      case 'network_info':
      case 'netstat':
        return commands.networkInfo;

      case 'search_text':
      case 'grep':
      case 'findstr':
        if (args && args.length >= 2) {
          const pattern = args[0];
          const files = args.slice(1).join(' ');
          if (platform === 'windows') {
            return `${commands.searchText} "${pattern}" ${files}`;
          } else {
            return `${commands.searchText} "${pattern}" ${files}`;
          }
        }
        return commands.searchText;

      case 'current_directory':
      case 'pwd':
      case 'cd':
        return commands.currentDirectory;

      default:
        // If no translation found, return the original intent
        return intent;
    }
  }

  static getPlatformInfo(): { platform: Platform; commands: CommandMapping } {
    return {
      platform: this.detectPlatform(),
      commands: this.getCommandMapping()
    };
  }

  static isWindows(): boolean {
    return this.detectPlatform() === 'windows';
  }

  static isUnix(): boolean {
    const platform = this.detectPlatform();
    return platform === 'macos' || platform === 'linux';
  }

  static normalizePath(path: string): string {
    const commands = this.getCommandMapping();
    if (this.isWindows()) {
      return path.replace(/\//g, commands.pathSeparator);
    } else {
      return path.replace(/\\/g, commands.pathSeparator);
    }
  }
}
