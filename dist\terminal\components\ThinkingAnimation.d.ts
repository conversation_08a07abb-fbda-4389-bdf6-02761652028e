export declare class ThinkingAnimation {
    private frames;
    private currentFrame;
    private startTime;
    private intervalId;
    private isRunning;
    start(message?: string): void;
    stop(): void;
    private updateFrame;
    isActive(): boolean;
    getElapsedTime(): number;
}
export declare class ProgressAnimation {
    private progressChars;
    private currentFrame;
    private intervalId;
    private isRunning;
    start(message: string, percentage?: number): void;
    update(message: string, percentage?: number): void;
    stop(): void;
    private updateFrame;
    private createProgressBar;
}
export declare class StatusIndicator {
    static success(message: string): void;
    static error(message: string): void;
    static warning(message: string): void;
    static info(message: string): void;
    static loading(message: string): void;
}
//# sourceMappingURL=ThinkingAnimation.d.ts.map