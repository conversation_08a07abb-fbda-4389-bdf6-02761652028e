declare module 'gradient-string' {
  interface GradientFunction {
    (text: string): string;
  }

  interface Gradient {
    rainbow: GradientFunction;
    pastel: GradientFunction;
    cristal: GradientFunction;
    teen: GradientFunction;
    mind: GradientFunction;
    morning: GradientFunction;
    vice: GradientFunction;
    passion: GradientFunction;
    fruit: GradientFunction;
    instagram: GradientFunction;
    atlas: GradientFunction;
    retro: GradientFunction;
    summer: GradientFunction;
    (colors: string[]): GradientFunction;
    (color1: string, color2: string, ...colors: string[]): GradientFunction;
  }

  const gradient: Gradient;
  export = gradient;
}
