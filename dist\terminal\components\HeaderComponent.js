import chalk from 'chalk';
import path from 'path';
import os from 'os';
export class HeaderComponent {
    static render(state) {
        const lines = [];
        // Top border
        lines.push(chalk.cyan('┌─────────────────────────────────────────────────────────────────────────────┐'));
        // Title line
        const title = 'Arien AI CLI';
        const titlePadding = Math.floor((77 - title.length) / 2);
        const titleLine = '│' +
            ' '.repeat(titlePadding) +
            chalk.bold.white(title) +
            ' '.repeat(77 - titlePadding - title.length) +
            '│';
        lines.push(chalk.cyan(titleLine));
        // Separator
        lines.push(chalk.cyan('├─────────────────────────────────────────────────────────────────────────────┤'));
        // Provider and Model line
        const providerText = `Provider: ${chalk.yellow(state.provider)}`;
        const modelText = `Model: ${chalk.yellow(state.model)}`;
        const statusText = state.isThinking ? chalk.red('● Thinking') : chalk.green('● Ready');
        const providerModelLine = '│ ' +
            providerText +
            ' '.repeat(25 - this.stripAnsi(providerText).length) +
            modelText +
            ' '.repeat(30 - this.stripAnsi(modelText).length) +
            statusText +
            ' '.repeat(77 - 2 - this.stripAnsi(providerText).length - 25 + this.stripAnsi(providerText).length - this.stripAnsi(modelText).length - 30 + this.stripAnsi(modelText).length - this.stripAnsi(statusText).length) +
            ' │';
        lines.push(chalk.cyan(providerModelLine));
        // Session and Directory line
        const sessionText = `Session: ${chalk.green(state.currentSession)}`;
        const dirText = `Dir: ${chalk.blue(this.formatPath(state.workingDirectory))}`;
        const sessionDirLine = '│ ' +
            sessionText +
            ' '.repeat(40 - this.stripAnsi(sessionText).length) +
            dirText +
            ' '.repeat(77 - 2 - this.stripAnsi(sessionText).length - 40 + this.stripAnsi(sessionText).length - this.stripAnsi(dirText).length) +
            ' │';
        lines.push(chalk.cyan(sessionDirLine));
        // Message count line
        const messageCount = state.messageHistory.length;
        const messageText = `Messages: ${chalk.magenta(messageCount.toString())}`;
        const timeText = `Time: ${chalk.gray(new Date().toLocaleTimeString())}`;
        const messageTimeLine = '│ ' +
            messageText +
            ' '.repeat(40 - this.stripAnsi(messageText).length) +
            timeText +
            ' '.repeat(77 - 2 - this.stripAnsi(messageText).length - 40 + this.stripAnsi(messageText).length - this.stripAnsi(timeText).length) +
            ' │';
        lines.push(chalk.cyan(messageTimeLine));
        // Bottom border
        lines.push(chalk.cyan('└─────────────────────────────────────────────────────────────────────────────┘'));
        return lines.join('\n');
    }
    static renderCompact(state) {
        const provider = chalk.yellow(state.provider);
        const model = chalk.yellow(state.model);
        const session = chalk.green(state.currentSession);
        const dir = chalk.blue(this.formatPath(state.workingDirectory, 30));
        const status = state.isThinking ? chalk.red('●') : chalk.green('●');
        return chalk.cyan('┤ ') +
            `${provider}:${model} ${status} ${session} ${dir}` +
            chalk.cyan(' ├');
    }
    static renderMinimal(state) {
        const status = state.isThinking ? chalk.red('●') : chalk.green('●');
        const provider = chalk.yellow(state.provider.substring(0, 3));
        const dir = chalk.blue(path.basename(state.workingDirectory));
        return `${status} ${provider} ${dir}`;
    }
    static formatPath(fullPath, maxLength = 50) {
        const homePath = os.homedir();
        let displayPath = fullPath;
        // Replace home directory with ~
        if (fullPath.startsWith(homePath)) {
            displayPath = '~' + fullPath.substring(homePath.length);
        }
        // Truncate if too long
        if (displayPath.length > maxLength) {
            const parts = displayPath.split(path.sep);
            if (parts.length > 3) {
                displayPath = parts[0] + path.sep + '...' + path.sep + parts.slice(-2).join(path.sep);
            }
            else {
                displayPath = '...' + displayPath.substring(displayPath.length - maxLength + 3);
            }
        }
        return displayPath;
    }
    static stripAnsi(text) {
        // Simple ANSI escape sequence removal
        return text.replace(/\x1b\[[0-9;]*m/g, '');
    }
    static renderSystemInfo() {
        const lines = [];
        lines.push(chalk.gray('System Information:'));
        lines.push(chalk.gray(`  OS: ${os.type()} ${os.release()}`));
        lines.push(chalk.gray(`  Platform: ${os.platform()} ${os.arch()}`));
        lines.push(chalk.gray(`  Node.js: ${process.version}`));
        lines.push(chalk.gray(`  Memory: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB used`));
        lines.push(chalk.gray(`  Uptime: ${Math.round(process.uptime())}s`));
        return lines.join('\n');
    }
    static renderHelp() {
        const lines = [];
        lines.push(chalk.cyan('🔧 Available Commands:'));
        lines.push('');
        lines.push(chalk.yellow('  /model <name>     ') + chalk.gray('Switch to a different model'));
        lines.push(chalk.yellow('  /provider <name>  ') + chalk.gray('Switch provider (deepseek/ollama)'));
        lines.push(chalk.yellow('  /session new      ') + chalk.gray('Start a new session'));
        lines.push(chalk.yellow('  /session clear    ') + chalk.gray('Clear current session'));
        lines.push(chalk.yellow('  /session save     ') + chalk.gray('Save session to file'));
        lines.push(chalk.yellow('  /history          ') + chalk.gray('Show message history'));
        lines.push(chalk.yellow('  /system           ') + chalk.gray('Show system information'));
        lines.push(chalk.yellow('  /help             ') + chalk.gray('Show this help message'));
        lines.push(chalk.yellow('  /exit             ') + chalk.gray('Exit the CLI'));
        lines.push('');
        lines.push(chalk.cyan('💡 Tips:'));
        lines.push(chalk.gray('  • Type your message and press Enter to chat'));
        lines.push(chalk.gray('  • Commands starting with / are slash commands'));
        lines.push(chalk.gray('  • Use Ctrl+C to interrupt current operation'));
        lines.push(chalk.gray('  • Use Ctrl+D or /exit to quit'));
        return lines.join('\n');
    }
    static renderBanner() {
        const lines = [];
        lines.push(chalk.cyan('╔══════════════════════════════════════════════════════════════════════════════╗'));
        lines.push(chalk.cyan('║') + chalk.bold.white('                              Arien AI CLI                               ') + chalk.cyan('║'));
        lines.push(chalk.cyan('║') + chalk.gray('                   Modern CLI with LLM Integration                       ') + chalk.cyan('║'));
        lines.push(chalk.cyan('╚══════════════════════════════════════════════════════════════════════════════╝'));
        return lines.join('\n');
    }
}
//# sourceMappingURL=HeaderComponent.js.map