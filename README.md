# Arien AI CLI

Modern and powerful CLI terminal system with LLM integration for function calling and shell command execution.

## 🚀 Features

- 🤖 **Advanced LLM Integration**: Support for Deepseek and Ollama providers with intelligent function calling
- 🛠️ **Smart Function Calling**: Execute shell commands with AI assistance and safety measures
- 🖥️ **Rich Terminal Interface**: Interactive terminal with real-time feedback, thinking animations, and enhanced UI components
- 📋 **Advanced Session Management**: Save, restore, and manage chat sessions with detailed history
- 🎯 **Powerful Slash Commands**: Quick actions with enhanced options and search capabilities
- 🔄 **Intelligent Retry Logic**: Advanced error handling with exponential backoff and circuit breaker patterns
- 🎨 **Enhanced Output Processing**: Formatted output with syntax highlighting, JSON formatting, and smart truncation
- 🔒 **Comprehensive Safety**: Built-in safety measures, command validation, and user approval workflows
- 📱 **Cross-Platform Support**: Works on Windows, macOS, Linux, and WSL with platform-specific optimizations
- 🔧 **Extensible Architecture**: Modular design with reusable components and utilities

## 📦 Installation

### Universal Installation Script

**Linux/macOS/WSL:**
```bash
curl -fsSL https://raw.githubusercontent.com/arien-ai/arien-ai-cli/main/install.sh | bash
```

**Windows PowerShell:**
```powershell
iwr -useb https://raw.githubusercontent.com/arien-ai/arien-ai-cli/main/install.ps1 | iex
```

### Manual Installation

**Using npm:**
```bash
npm install -g arien-ai-cli
```

**From Source:**
```bash
git clone https://github.com/arien-ai/arien-ai-cli.git
cd arien-ai-cli
npm install
npm run build
npm install -g .
```

### Platform-Specific Notes

**Windows 11 WSL:**
- Supports both WSL1 and WSL2
- Access Windows files from `/mnt/c/`
- Consider using Windows Terminal for better experience

**macOS:**
- Requires Xcode Command Line Tools
- Homebrew integration available

**Linux:**
- Supports major distributions (Ubuntu, Debian, CentOS, Arch)
- Package manager detection and integration

## Setup

After installation, run the setup wizard to configure your LLM providers:

```bash
arien setup
```

### Supported Providers

#### Deepseek
- Models: `deepseek-chat`, `deepseek-reasoner`
- Requires API key from [Deepseek Platform](https://platform.deepseek.com/)
- API endpoint: `https://api.deepseek.com`

#### Ollama
- Local LLM provider
- Requires [Ollama](https://ollama.ai/) installation
- Default endpoint: `http://localhost:11434`
- Pull models with: `ollama pull llama2`

## Usage

### Interactive Mode

Start the interactive chat mode:

```bash
arien
# or
arien chat
```

### Slash Commands

Available commands in interactive mode:

- `/model <name>` - Switch to a different model
- `/provider <name>` - Switch provider (deepseek/ollama)
- `/session new` - Start a new session
- `/session clear` - Clear current session
- `/session save` - Save session to file
- `/history` - Show message history
- `/system` - Show system information
- `/debug` - Toggle debug/tool visibility settings
  - `/debug tools` - Toggle tool details visibility
  - `/debug execution` - Toggle execution details visibility
  - `/debug enable` - Enable all debug output
  - `/debug disable` - Disable all debug output
- `/help` - Show help message
- `/exit` - Exit the CLI

### CLI Commands

- `arien setup` - Run setup wizard
- `arien config show` - Show current configuration
- `arien config reset` - Reset configuration
- `arien info` - Show system information
- `arien version` - Show version information
- `arien help` - Show help

## Function Calling

The CLI includes a powerful shell tool that can execute commands with AI assistance:

### Shell Tool Features

- **Cross-platform compatibility**: Works on Windows, macOS, and Linux
- **Safety checks**: Validates commands for dangerous operations
- **Timeout handling**: Prevents hanging commands
- **Rich output formatting**: Structured display of command results
- **Error handling**: Graceful handling of command failures

### When to Use Shell Commands

✅ **Recommended uses:**
- File system operations (`ls`, `dir`, `find`)
- Package management (`npm`, `pip`, `apt`, `brew`)
- Git operations (`git status`, `git add`, `git commit`)
- Build and compilation tasks
- System information gathering
- Text processing and file manipulation

❌ **Use with caution:**
- Destructive operations (`rm -rf`, `del /f /s /q`)
- System-critical modifications
- Commands requiring interactive input
- Long-running services

### Example Interactions

**Default Mode (Clean Output):**
```
You: List all TypeScript files in the current directory
Assistant: I'll help you find all TypeScript files in the current directory.

./src/index.ts
./src/terminal/TerminalInterface.ts
./src/providers/DeepseekProvider.ts
...
```

**Debug Mode (with `/debug enable`):**
```
You: List all TypeScript files in the current directory
Assistant: I'll help you find all TypeScript files in the current directory.

🛠️  execute_shell_command (45ms)
✅ Success:

📤 Output:
   ./src/index.ts
   ./src/terminal/TerminalInterface.ts
   ./src/providers/DeepseekProvider.ts
   ...

📊 Execution Details:
   Command: find . -name "*.ts" -type f
   Exit Code: 0
   Duration: 45ms
   Status: ✅ Success
```

## Configuration

Configuration is stored in `~/.arien-ai/config.yaml`:

```yaml
currentProvider: deepseek
currentModel: deepseek-chat
providers:
  deepseek:
    name: deepseek
    models:
      - deepseek-chat
      - deepseek-reasoner
    apiKey: your-api-key
    baseUrl: https://api.deepseek.com
  ollama:
    name: ollama
    models:
      - llama2
      - codellama
    baseUrl: http://localhost:11434
sessionHistory: []
workingDirectory: /current/working/directory
autoApprove: false
maxRetries: 3
retryDelay: 1000
showToolDetails: false
showToolExecutionDetails: false
```

## Development

### Building

```bash
npm run build
```

### Development Mode

```bash
npm run dev
```

### Testing

```bash
npm test
```

### Linting

```bash
npm run lint
```

## Error Handling

The CLI includes comprehensive error handling:

- **Network errors**: Automatic retry with exponential backoff
- **Rate limiting**: Intelligent retry delays
- **Command failures**: Clear error messages and suggestions
- **Configuration issues**: Helpful setup guidance

## Security

- **Command validation**: Prevents execution of dangerous commands
- **Approval system**: User confirmation for risky operations
- **API key protection**: Secure storage of credentials
- **Input sanitization**: Protection against shell injection

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Support

- GitHub Issues: [Report bugs or request features](https://github.com/arien-ai/arien-ai-cli/issues)
- Documentation: [Wiki](https://github.com/arien-ai/arien-ai-cli/wiki)

## Changelog

### v1.0.0
- Initial release
- Deepseek and Ollama provider support
- Shell command execution
- Interactive terminal interface
- Cross-platform installation script
- Comprehensive error handling and retry logic
