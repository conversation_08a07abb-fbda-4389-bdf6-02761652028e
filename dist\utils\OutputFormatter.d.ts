import { ShellResult, Tool<PERSON><PERSON>ult } from '../types/index.js';
export declare class OutputFormatter {
    static formatShellResult(result: ShellResult): string;
    static formatToolResult(result: ToolResult): string;
    private static formatOutput;
    private static formatToolOutput;
    static formatThinking(message: string): string;
    static formatUserMessage(message: string): string;
    static formatAssistantMessage(message: string): string;
    static formatSystemMessage(message: string): string;
    static formatHeader(provider: string, model: string, cwd: string, session: string): string;
    static formatSlashCommands(): string;
    private static truncatePath;
    static stripAnsiCodes(text: string): string;
    static formatProgress(message: string, percentage?: number): string;
    static formatWarning(message: string): string;
    static formatError(message: string): string;
    static formatSuccess(message: string): string;
}
//# sourceMappingURL=OutputFormatter.d.ts.map