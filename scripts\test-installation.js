#!/usr/bin/env node

import { execSync, spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

class InstallationTester {
  constructor() {
    this.colors = {
      reset: '\x1b[0m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      cyan: '\x1b[36m'
    };
    this.testResults = [];
  }

  log(message, color = 'reset') {
    console.log(`${this.colors[color]}${message}${this.colors.reset}`);
  }

  async runTest(name, testFn) {
    this.log(`\n🧪 Testing: ${name}`, 'cyan');
    try {
      await testFn();
      this.log(`✅ ${name} - PASSED`, 'green');
      this.testResults.push({ name, status: 'PASSED' });
    } catch (error) {
      this.log(`❌ ${name} - FAILED: ${error.message}`, 'red');
      this.testResults.push({ name, status: 'FAILED', error: error.message });
    }
  }

  async testPrerequisites() {
    // Test Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 20) {
      throw new Error(`Node.js ${nodeVersion} is too old. Requires 20+`);
    }
    this.log(`Node.js ${nodeVersion} ✓`);

    // Test npm
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    this.log(`npm ${npmVersion} ✓`);

    // Test TypeScript compilation
    if (!fs.existsSync('tsconfig.json')) {
      throw new Error('tsconfig.json not found');
    }
    this.log('TypeScript config ✓');
  }

  async testBuild() {
    this.log('Building project...', 'yellow');
    execSync('npm run build', { stdio: 'inherit' });
    
    if (!fs.existsSync('dist/index.js')) {
      throw new Error('Build output not found');
    }
    this.log('Build output exists ✓');
  }

  async testConfigManager() {
    const { ConfigManager } = await import('../dist/config/ConfigManager.js');
    const configManager = new ConfigManager();
    
    // Test config loading
    const config = await configManager.loadConfig();
    if (!config) {
      throw new Error('Config loading failed');
    }
    this.log('Config loading ✓');

    // Test config structure
    const requiredFields = ['currentProvider', 'currentModel', 'providers', 'sessionHistory'];
    for (const field of requiredFields) {
      if (!(field in config)) {
        throw new Error(`Missing config field: ${field}`);
      }
    }
    this.log('Config structure ✓');
  }

  async testErrorHandler() {
    const { ErrorHandler } = await import('../dist/utils/ErrorHandler.js');
    const errorHandler = new ErrorHandler();

    // Test retry logic
    let attempts = 0;
    try {
      const result = await errorHandler.executeWithRetry(async () => {
        attempts++;
        if (attempts < 2) {
          const error = new Error('Connection reset');
          error.code = 'ECONNRESET';
          throw error;
        }
        return 'success';
      });

      if (result !== 'success') {
        throw new Error('Retry did not return expected result');
      }
    } catch (error) {
      throw new Error(`Retry logic failed: ${error.message}`);
    }

    if (attempts !== 2) {
      throw new Error(`Expected 2 attempts, got ${attempts}`);
    }
    this.log('Retry logic ✓');
  }

  async testShellTool() {
    const { ShellTool } = await import('../dist/tools/ShellTool.js');
    const shellTool = new ShellTool();
    
    // Test command validation
    const validation = shellTool.validateCommand('ls -la');
    if (!validation.valid) {
      throw new Error('Valid command rejected');
    }
    this.log('Command validation ✓');

    // Test tool definition
    const toolDef = ShellTool.getToolDefinition();
    if (!toolDef.name || !toolDef.description || !toolDef.parameters) {
      throw new Error('Invalid tool definition');
    }
    this.log('Tool definition ✓');

    // Test safe command execution
    const result = await shellTool.execute({
      command: os.platform() === 'win32' ? 'echo test' : 'echo test'
    });
    
    if (!result.success || !result.stdout.includes('test')) {
      throw new Error('Command execution failed');
    }
    this.log('Command execution ✓');
  }

  async testOutputFormatter() {
    const { OutputFormatter } = await import('../dist/utils/OutputFormatter.js');
    
    // Test shell result formatting
    const shellResult = {
      success: true,
      stdout: 'test output',
      stderr: '',
      exitCode: 0,
      executionTime: 100,
      command: 'echo test'
    };
    
    const formatted = OutputFormatter.formatShellResult(shellResult);
    if (!formatted.includes('test output') || !formatted.includes('echo test')) {
      throw new Error('Shell result formatting failed');
    }
    this.log('Output formatting ✓');
  }

  async testTerminalComponents() {
    // Test ThinkingAnimation
    const { ThinkingAnimation } = await import('../dist/terminal/components/ThinkingAnimation.js');
    const animation = new ThinkingAnimation();
    
    if (animation.isActive()) {
      throw new Error('Animation should not be active initially');
    }
    this.log('ThinkingAnimation ✓');

    // Test HeaderComponent
    const { HeaderComponent } = await import('../dist/terminal/components/HeaderComponent.js');
    const state = {
      isThinking: false,
      currentSession: 'test-session',
      messageHistory: [],
      workingDirectory: process.cwd(),
      provider: 'deepseek',
      model: 'deepseek-chat'
    };
    
    const header = HeaderComponent.render(state);
    if (!header.includes('Arien AI CLI') || !header.includes('deepseek')) {
      throw new Error('Header rendering failed');
    }
    this.log('HeaderComponent ✓');
  }

  async testProviders() {
    // Test DeepseekProvider (without API key)
    const { DeepseekProvider } = await import('../dist/providers/DeepseekProvider.js');
    const deepseekConfig = {
      name: 'deepseek',
      models: ['deepseek-chat', 'deepseek-reasoner'],
      apiKey: 'test-key',
      baseUrl: 'https://api.deepseek.com'
    };
    
    const deepseekProvider = new DeepseekProvider(deepseekConfig);
    const config = deepseekProvider.getConfig();
    if (config.name !== 'deepseek' || config.apiKey !== 'test-key') {
      throw new Error('DeepseekProvider config failed');
    }
    this.log('DeepseekProvider ✓');

    // Test OllamaProvider
    const { OllamaProvider } = await import('../dist/providers/OllamaProvider.js');
    const ollamaConfig = {
      name: 'ollama',
      models: ['llama2'],
      baseUrl: 'http://localhost:11434'
    };
    
    const ollamaProvider = new OllamaProvider(ollamaConfig);
    const ollamaConfigResult = ollamaProvider.getConfig();
    if (ollamaConfigResult.name !== 'ollama' || ollamaConfigResult.baseUrl !== 'http://localhost:11434') {
      throw new Error('OllamaProvider config failed');
    }
    this.log('OllamaProvider ✓');
  }

  async testSystemPrompt() {
    const { getSystemPrompt } = await import('../dist/prompts/SystemPrompt.js');
    const prompt = getSystemPrompt();
    
    if (!prompt || prompt.length < 100) {
      throw new Error('System prompt is too short or missing');
    }
    
    if (!prompt.includes('shell') || !prompt.includes('command')) {
      throw new Error('System prompt missing key terms');
    }
    this.log('System prompt ✓');
  }

  async testCLIEntry() {
    // Test that the main entry point can be imported
    try {
      // Just test import, don't run the CLI
      const indexPath = path.resolve('dist/index.js');
      if (!fs.existsSync(indexPath)) {
        throw new Error('CLI entry point not found');
      }
      this.log('CLI entry point ✓');
    } catch (error) {
      throw new Error(`CLI entry point test failed: ${error.message}`);
    }
  }

  async runAllTests() {
    this.log('🚀 Starting Installation Tests', 'cyan');
    this.log('================================', 'cyan');

    await this.runTest('Prerequisites', () => this.testPrerequisites());
    await this.runTest('Build Process', () => this.testBuild());
    await this.runTest('Config Manager', () => this.testConfigManager());
    await this.runTest('Error Handler', () => this.testErrorHandler());
    await this.runTest('Shell Tool', () => this.testShellTool());
    await this.runTest('Output Formatter', () => this.testOutputFormatter());
    await this.runTest('Terminal Components', () => this.testTerminalComponents());
    await this.runTest('LLM Providers', () => this.testProviders());
    await this.runTest('System Prompt', () => this.testSystemPrompt());
    await this.runTest('CLI Entry Point', () => this.testCLIEntry());

    this.showResults();
  }

  showResults() {
    this.log('\n📊 Test Results', 'cyan');
    this.log('================', 'cyan');

    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;

    this.testResults.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' : '❌';
      const color = result.status === 'PASSED' ? 'green' : 'red';
      this.log(`${icon} ${result.name}`, color);
      if (result.error) {
        this.log(`   Error: ${result.error}`, 'red');
      }
    });

    this.log(`\n📈 Summary: ${passed} passed, ${failed} failed`, failed > 0 ? 'red' : 'green');

    if (failed > 0) {
      this.log('\n❌ Some tests failed. Please fix the issues before proceeding.', 'red');
      process.exit(1);
    } else {
      this.log('\n🎉 All tests passed! Installation is ready.', 'green');
    }
  }
}

// Run tests
const tester = new InstallationTester();
tester.runAllTests().catch(error => {
  console.error('❌ Test runner failed:', error.message);
  process.exit(1);
});
