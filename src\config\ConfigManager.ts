import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import yaml from 'yaml';
import { CliConfig, DeepseekConfig, OllamaConfig } from '../types/index.js';

export class ConfigManager {
  private configPath: string;
  private config: CliConfig;

  constructor() {
    this.configPath = path.join(os.homedir(), '.arien-ai', 'config.yaml');
    this.config = this.getDefaultConfig();
  }

  private getDefaultConfig(): CliConfig {
    return {
      currentProvider: 'deepseek',
      currentModel: 'deepseek-chat',
      providers: {},
      sessionHistory: [],
      workingDirectory: process.cwd(),
      autoApprove: false,
      maxRetries: 3,
      retryDelay: 1000,
      showToolDetails: false,
      showToolExecutionDetails: false
    };
  }

  async loadConfig(): Promise<CliConfig> {
    try {
      await fs.ensureDir(path.dirname(this.configPath));
      
      if (await fs.pathExists(this.configPath)) {
        const configData = await fs.readFile(this.configPath, 'utf-8');
        const loadedConfig = yaml.parse(configData) as CliConfig;
        
        // Merge with defaults to ensure all properties exist
        this.config = { ...this.getDefaultConfig(), ...loadedConfig };
      } else {
        // Create default config file
        await this.saveConfig();
      }
    } catch (error) {
      console.error('Error loading config:', error);
      this.config = this.getDefaultConfig();
    }
    
    return this.config;
  }

  async saveConfig(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.configPath));
      const configYaml = yaml.stringify(this.config, { indent: 2 });
      await fs.writeFile(this.configPath, configYaml, 'utf-8');
    } catch (error) {
      console.error('Error saving config:', error);
      throw error;
    }
  }

  getConfig(): CliConfig {
    return this.config;
  }

  async updateProvider(provider: 'deepseek' | 'ollama', config: DeepseekConfig | OllamaConfig): Promise<void> {
    if (provider === 'deepseek') {
      this.config.providers[provider] = config as DeepseekConfig;
    } else {
      this.config.providers[provider] = config as OllamaConfig;
    }
    this.config.currentProvider = provider;
    
    if (provider === 'deepseek' && config.models.length > 0) {
      this.config.currentModel = config.models[0] as string;
    } else if (provider === 'ollama' && config.models.length > 0) {
      this.config.currentModel = config.models[0] as string;
    }
    
    await this.saveConfig();
  }

  async updateCurrentModel(model: string): Promise<void> {
    this.config.currentModel = model;
    await this.saveConfig();
  }

  async updateCurrentProvider(provider: 'deepseek' | 'ollama'): Promise<void> {
    if (this.config.providers[provider]) {
      this.config.currentProvider = provider;
      const providerConfig = this.config.providers[provider];
      if (providerConfig && providerConfig.models.length > 0) {
        this.config.currentModel = providerConfig.models[0] as string;
      }
      await this.saveConfig();
    } else {
      throw new Error(`Provider ${provider} is not configured`);
    }
  }

  async addMessageToHistory(message: any): Promise<void> {
    this.config.sessionHistory.push(message);
    
    // Keep only last 100 messages to prevent config file from growing too large
    if (this.config.sessionHistory.length > 100) {
      this.config.sessionHistory = this.config.sessionHistory.slice(-100);
    }
    
    await this.saveConfig();
  }

  async clearHistory(): Promise<void> {
    this.config.sessionHistory = [];
    await this.saveConfig();
  }

  async updateWorkingDirectory(directory: string): Promise<void> {
    this.config.workingDirectory = directory;
    await this.saveConfig();
  }

  async toggleAutoApprove(): Promise<boolean> {
    this.config.autoApprove = !this.config.autoApprove;
    await this.saveConfig();
    return this.config.autoApprove;
  }

  getCurrentProvider(): DeepseekConfig | OllamaConfig | undefined {
    return this.config.providers[this.config.currentProvider];
  }

  isProviderConfigured(provider: 'deepseek' | 'ollama'): boolean {
    return !!this.config.providers[provider];
  }

  getConfigPath(): string {
    return this.configPath;
  }

  async toggleToolDetails(): Promise<boolean> {
    this.config.showToolDetails = !this.config.showToolDetails;
    await this.saveConfig();
    return this.config.showToolDetails;
  }

  async toggleToolExecutionDetails(): Promise<boolean> {
    this.config.showToolExecutionDetails = !this.config.showToolExecutionDetails;
    await this.saveConfig();
    return this.config.showToolExecutionDetails;
  }

  async setToolDetailsVisibility(showDetails: boolean, showExecutionDetails: boolean): Promise<void> {
    this.config.showToolDetails = showDetails;
    this.config.showToolExecutionDetails = showExecutionDetails;
    await this.saveConfig();
  }
}
