{"version": 3, "file": "HeaderComponent.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/HeaderComponent.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,MAAM,IAAI,CAAC;AAGpB,MAAM,OAAO,eAAe;IAC1B,MAAM,CAAC,MAAM,CAAC,KAAoB;QAChC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,aAAa;QACb,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC,CAAC;QAE1G,aAAa;QACb,MAAM,KAAK,GAAG,cAAc,CAAC;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QACzD,MAAM,SAAS,GAAG,GAAG;YACnB,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;YACvB,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC;YAC5C,GAAG,CAAC;QACN,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAElC,YAAY;QACZ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC,CAAC;QAE1G,0BAA0B;QAC1B,MAAM,YAAY,GAAG,aAAa,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjE,MAAM,SAAS,GAAG,UAAU,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEvF,MAAM,iBAAiB,GAAG,IAAI;YAC5B,YAAY;YACZ,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;YACpD,SAAS;YACT,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;YACjD,UAAU;YACV,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;YAClN,IAAI,CAAC;QACP,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAE1C,6BAA6B;QAC7B,MAAM,WAAW,GAAG,YAAY,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;QACpE,MAAM,OAAO,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC;QAE9E,MAAM,cAAc,GAAG,IAAI;YACzB,WAAW;YACX,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;YACnD,OAAO;YACP,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YAClI,IAAI,CAAC;QACP,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAEvC,qBAAqB;QACrB,MAAM,YAAY,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;QACjD,MAAM,WAAW,GAAG,aAAa,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;QAC1E,MAAM,QAAQ,GAAG,SAAS,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC;QAExE,MAAM,eAAe,GAAG,IAAI;YAC1B,WAAW;YACX,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;YACnD,QAAQ;YACR,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;YACnI,IAAI,CAAC;QACP,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QAExC,gBAAgB;QAChB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iFAAiF,CAAC,CAAC,CAAC;QAE1G,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,KAAoB;QACvC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,CAAC;QACpE,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEpE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YAChB,GAAG,QAAQ,IAAI,KAAK,IAAI,MAAM,IAAI,OAAO,IAAI,GAAG,EAAE;YAClD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,KAAoB;QACvC,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAE9D,OAAO,GAAG,MAAM,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;IACxC,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,QAAgB,EAAE,YAAoB,EAAE;QAChE,MAAM,QAAQ,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,gCAAgC;QAChC,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,WAAW,GAAG,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;QAED,uBAAuB;QACvB,IAAI,WAAW,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxF,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,MAAM,CAAC,SAAS,CAAC,IAAY;QACnC,sCAAsC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,gBAAgB;QACrB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;QAC9C,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QAC7D,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QACpE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACxD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACvG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAErE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,UAAU;QACf,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACjD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;QAC7F,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC,CAAC;QACnG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;QACrF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;QACvF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACtF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;QACtF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACzF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACxF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAC9E,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QACnC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC,CAAC;QACxE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC,CAAC;QAC1E,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC,CAAC;QACxE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC;QAE1D,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,YAAY;QACjB,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC,CAAC;QAC3G,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,2EAA2E,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9I,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,2EAA2E,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACxI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC,CAAC;QAE3G,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF"}