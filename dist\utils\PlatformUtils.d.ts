export type Platform = 'windows' | 'macos' | 'linux' | 'unknown';
export interface CommandMapping {
    listFiles: string;
    listFilesDetailed: string;
    showFileContent: string;
    findFiles: string;
    listProcesses: string;
    findProcess: string;
    systemInfo: string;
    networkInfo: string;
    searchText: string;
    createDirectory: string;
    removeDirectory: string;
    copyFile: string;
    moveFile: string;
    deleteFile: string;
    currentDirectory: string;
    changeDirectory: string;
    pathSeparator: string;
}
export declare class PlatformUtils {
    private static platform;
    private static commandMap;
    static detectPlatform(): Platform;
    static getCommandMapping(): CommandMapping;
    static translateCommand(intent: string, args?: string[]): string;
    static getPlatformInfo(): {
        platform: Platform;
        commands: CommandMapping;
    };
    static isWindows(): boolean;
    static isUnix(): boolean;
    static normalizePath(path: string): string;
}
//# sourceMappingURL=PlatformUtils.d.ts.map