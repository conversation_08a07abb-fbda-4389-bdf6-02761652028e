import chalk from 'chalk';
import stripAnsi from 'strip-ansi';
export class OutputFormatter {
    static formatShellResult(result) {
        const lines = [];
        // Command header
        lines.push(chalk.cyan('🔧 Command Executed:'));
        lines.push(chalk.gray(`   ${result.command}`));
        lines.push('');
        // Execution info
        lines.push(chalk.blue('📊 Execution Info:'));
        lines.push(chalk.gray(`   Exit Code: ${result.exitCode}`));
        lines.push(chalk.gray(`   Execution Time: ${result.executionTime}ms`));
        lines.push(chalk.gray(`   Success: ${result.success ? '✅' : '❌'}`));
        lines.push('');
        // Standard output
        if (result.stdout.trim()) {
            lines.push(chalk.green('📤 Standard Output:'));
            lines.push(this.formatOutput(result.stdout, 'stdout'));
            lines.push('');
        }
        // Standard error
        if (result.stderr.trim()) {
            lines.push(chalk.red('📥 Standard Error:'));
            lines.push(this.formatOutput(result.stderr, 'stderr'));
            lines.push('');
        }
        return lines.join('\n');
    }
    static formatToolResult(result) {
        const lines = [];
        // Tool header
        lines.push(chalk.magenta('🛠️  Tool Executed:'));
        lines.push(chalk.gray(`   Name: ${result.name}`));
        lines.push(chalk.gray(`   ID: ${result.id}`));
        lines.push(chalk.gray(`   Success: ${result.success ? '✅' : '❌'}`));
        lines.push(chalk.gray(`   Execution Time: ${result.executionTime}ms`));
        lines.push('');
        // Result content
        if (result.success) {
            lines.push(chalk.green('📋 Result:'));
            lines.push(this.formatToolOutput(result.result));
        }
        else {
            lines.push(chalk.red('❌ Error:'));
            lines.push(chalk.red(`   ${result.error || 'Unknown error'}`));
        }
        return lines.join('\n');
    }
    static formatOutput(output, type) {
        const lines = output.split('\n');
        const color = type === 'stdout' ? chalk.white : chalk.red;
        return lines
            .map(line => `   ${color(line)}`)
            .join('\n');
    }
    static formatToolOutput(output) {
        if (typeof output === 'string') {
            return output.split('\n')
                .map(line => `   ${chalk.white(line)}`)
                .join('\n');
        }
        else if (typeof output === 'object') {
            try {
                const jsonStr = JSON.stringify(output, null, 2);
                return jsonStr.split('\n')
                    .map(line => `   ${chalk.white(line)}`)
                    .join('\n');
            }
            catch {
                return `   ${chalk.white(String(output))}`;
            }
        }
        else {
            return `   ${chalk.white(String(output))}`;
        }
    }
    static formatThinking(message) {
        return chalk.yellow(`🤔 ${message}`);
    }
    static formatUserMessage(message) {
        return chalk.blue(`👤 You: ${message}`);
    }
    static formatAssistantMessage(message) {
        return chalk.green(`🤖 Assistant: ${message}`);
    }
    static formatSystemMessage(message) {
        return chalk.gray(`⚙️  System: ${message}`);
    }
    static formatHeader(provider, model, cwd, session) {
        const lines = [];
        lines.push(chalk.cyan('┌─────────────────────────────────────────────────────────────┐'));
        lines.push(chalk.cyan('│') + chalk.bold.white('                    Arien AI CLI                        ') + chalk.cyan('│'));
        lines.push(chalk.cyan('├─────────────────────────────────────────────────────────────┤'));
        lines.push(chalk.cyan('│') + ` Provider: ${chalk.yellow(provider.padEnd(10))} Model: ${chalk.yellow(model.padEnd(20))} ` + chalk.cyan('│'));
        lines.push(chalk.cyan('│') + ` Session: ${chalk.green(session.padEnd(47))} ` + chalk.cyan('│'));
        lines.push(chalk.cyan('│') + ` Working Dir: ${chalk.blue(this.truncatePath(cwd, 43))} ` + chalk.cyan('│'));
        lines.push(chalk.cyan('└─────────────────────────────────────────────────────────────┘'));
        return lines.join('\n');
    }
    static formatSlashCommands() {
        const commands = [
            { cmd: '/model <name>', desc: 'Switch to a different model' },
            { cmd: '/provider <name>', desc: 'Switch to a different provider (deepseek/ollama)' },
            { cmd: '/session <action>', desc: 'Session management (new/clear/save)' },
            { cmd: '/history', desc: 'Show message history' },
            { cmd: '/help', desc: 'Show this help message' },
            { cmd: '/exit', desc: 'Exit the CLI' }
        ];
        const lines = [];
        lines.push(chalk.cyan('📋 Available Slash Commands:'));
        lines.push('');
        commands.forEach(({ cmd, desc }) => {
            lines.push(`   ${chalk.yellow(cmd.padEnd(20))} ${chalk.gray(desc)}`);
        });
        return lines.join('\n');
    }
    static truncatePath(path, maxLength) {
        if (path.length <= maxLength) {
            return path.padEnd(maxLength);
        }
        const truncated = '...' + path.slice(-(maxLength - 3));
        return truncated.padEnd(maxLength);
    }
    static stripAnsiCodes(text) {
        return stripAnsi(text);
    }
    static formatProgress(message, percentage) {
        const progressBar = percentage !== undefined ?
            ` [${chalk.green('█'.repeat(Math.floor(percentage / 10)))}${chalk.gray('░'.repeat(10 - Math.floor(percentage / 10)))}] ${percentage}%` :
            '';
        return chalk.blue(`⏳ ${message}${progressBar}`);
    }
    static formatWarning(message) {
        return chalk.yellow(`⚠️  ${message}`);
    }
    static formatError(message) {
        return chalk.red(`❌ ${message}`);
    }
    static formatSuccess(message) {
        return chalk.green(`✅ ${message}`);
    }
}
//# sourceMappingURL=OutputFormatter.js.map