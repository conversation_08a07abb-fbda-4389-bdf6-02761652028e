{"name": "arien-ai-cli", "version": "1.0.0", "type": "module", "description": "Modern and powerful CLI terminal system with LLM integration for function calling and shell command execution", "main": "dist/index.js", "bin": {"arien": "dist/index.js", "arien-ai": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "install-global": "npm run build && npm install -g .", "uninstall-global": "npm uninstall -g arien-ai-cli", "test": "node scripts/test-installation.js", "test:full": "npm run build && npm run test", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "prepare": "npm run build", "prepublishOnly": "npm run test:full && npm run lint && npm run format:check"}, "keywords": ["cli", "terminal", "ai", "llm", "deepseek", "ollama", "function-calling", "shell", "automation"], "author": "Arien AI", "license": "MIT", "engines": {"node": ">=20.0.0"}, "dependencies": {"chalk": "^5.3.0", "commander": "^12.0.0", "inquirer": "^9.2.15", "axios": "^1.6.7", "node-fetch": "^3.3.2", "ora": "^8.0.1", "boxen": "^7.1.1", "figlet": "^1.7.0", "gradient-string": "^2.0.2", "cli-table3": "^0.6.3", "yaml": "^2.4.1", "fs-extra": "^11.2.0", "cross-spawn": "^7.0.3", "strip-ansi": "^7.1.0", "terminal-kit": "^3.0.1", "blessed": "^0.1.81", "neo-blessed": "^0.2.0"}, "devDependencies": {"@types/node": "^20.11.24", "@types/inquirer": "^9.0.7", "@types/figlet": "^1.5.8", "@types/fs-extra": "^11.0.4", "@types/cross-spawn": "^6.0.6", "@types/blessed": "^0.1.25", "@types/gradient-string": "^1.1.6", "typescript": "^5.8.3", "tsx": "^4.7.1", "rimraf": "^5.0.5", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "prettier": "^3.2.5", "jest": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.1.2"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-ai-cli.git"}, "bugs": {"url": "https://github.com/arien-ai/arien-ai-cli/issues"}, "homepage": "https://github.com/arien-ai/arien-ai-cli#readme"}