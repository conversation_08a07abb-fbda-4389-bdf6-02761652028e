import chalk from 'chalk';
export class ThinkingAnimation {
    frames = [
        "( ●    )",
        "(  ●   )",
        "(   ●  )",
        "(    ● )",
        "(     ●)",
        "(    ● )",
        "(   ●  )",
        "(  ●   )",
        "( ●    )",
        "(●     )",
    ];
    currentFrame = 0;
    startTime = 0;
    intervalId = null;
    isRunning = false;
    start(message = 'Thinking') {
        if (this.isRunning) {
            return;
        }
        this.isRunning = true;
        this.startTime = Date.now();
        this.currentFrame = 0;
        // Hide cursor
        process.stdout.write('\x1B[?25l');
        this.intervalId = setInterval(() => {
            this.updateFrame(message);
        }, 150);
    }
    stop() {
        if (!this.isRunning) {
            return;
        }
        this.isRunning = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        // Clear the line and show cursor
        process.stdout.write('\r\x1B[K');
        process.stdout.write('\x1B[?25h');
    }
    updateFrame(message) {
        const elapsedSeconds = Math.floor((Date.now() - this.startTime) / 1000);
        const frame = this.frames[this.currentFrame];
        const output = chalk.yellow(`🤖 ${message} ${frame} ${chalk.gray(`(${elapsedSeconds}s)`)}`);
        // Move to beginning of line and clear it, then write new content
        process.stdout.write(`\r\x1B[K${output}`);
        this.currentFrame = (this.currentFrame + 1) % this.frames.length;
    }
    isActive() {
        return this.isRunning;
    }
    getElapsedTime() {
        return this.isRunning ? Date.now() - this.startTime : 0;
    }
}
export class ProgressAnimation {
    progressChars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    currentFrame = 0;
    intervalId = null;
    isRunning = false;
    start(message, percentage) {
        if (this.isRunning) {
            return;
        }
        this.isRunning = true;
        this.currentFrame = 0;
        // Hide cursor
        process.stdout.write('\x1B[?25l');
        this.intervalId = setInterval(() => {
            this.updateFrame(message, percentage);
        }, 100);
    }
    update(message, percentage) {
        if (this.isRunning) {
            this.updateFrame(message, percentage);
        }
    }
    stop() {
        if (!this.isRunning) {
            return;
        }
        this.isRunning = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        // Clear the line and show cursor
        process.stdout.write('\r\x1B[K');
        process.stdout.write('\x1B[?25h');
    }
    updateFrame(message, percentage) {
        const spinner = this.progressChars[this.currentFrame];
        let output = chalk.blue(`${spinner} ${message}`);
        if (percentage !== undefined) {
            const progressBar = this.createProgressBar(percentage);
            output += ` ${progressBar} ${percentage}%`;
        }
        // Move to beginning of line and clear it, then write new content
        process.stdout.write(`\r\x1B[K${output}`);
        this.currentFrame = (this.currentFrame + 1) % this.progressChars.length;
    }
    createProgressBar(percentage) {
        const width = 20;
        const filled = Math.floor((percentage / 100) * width);
        const empty = width - filled;
        return chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
    }
}
export class StatusIndicator {
    static success(message) {
        console.log(chalk.green(`✅ ${message}`));
    }
    static error(message) {
        console.log(chalk.red(`❌ ${message}`));
    }
    static warning(message) {
        console.log(chalk.yellow(`⚠️  ${message}`));
    }
    static info(message) {
        console.log(chalk.blue(`ℹ️  ${message}`));
    }
    static loading(message) {
        console.log(chalk.cyan(`⏳ ${message}`));
    }
}
//# sourceMappingURL=ThinkingAnimation.js.map