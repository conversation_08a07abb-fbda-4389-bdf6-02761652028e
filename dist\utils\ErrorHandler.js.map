{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAG1B,MAAM,OAAO,YAAY;IACf,WAAW,CAAc;IAEjC,YAAY,WAAkC;QAC5C,IAAI,CAAC,WAAW,GAAG;YACjB,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,iBAAiB,EAAE,CAAC;YACpB,eAAe,EAAE;gBACf,YAAY;gBACZ,WAAW;gBACX,cAAc;gBACd,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,OAAO;gBACP,YAAY;gBACZ,eAAe;gBACf,iBAAiB;aAClB;YACD,GAAG,WAAW;SACf,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,SAA2B,EAC3B,UAAkB,WAAW;QAE7B,IAAI,SAA4B,CAAC;QAEjC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACxE,IAAI,CAAC;gBACH,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAE3B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAc,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;oBACtF,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC3C,OAAO,CAAC,GAAG,CACT,KAAK,CAAC,MAAM,CACV,OAAO,OAAO,oBAAoB,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,kBAAkB,KAAK,OAAO,CACvG,CACF,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,aAAc,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAEjE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,KAAY;QACnC,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,SAAS,GAAI,KAAa,CAAC,IAAI,CAAC;QAEtC,iCAAiC;QACjC,IAAI,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,kCAAkC;QAClC,IAAK,KAAa,CAAC,YAAY,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,KAAY,CAAC;YAEhC,2BAA2B;YAC3B,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC1E,OAAO,IAAI,CAAC;YACd,CAAC;YAED,qDAAqD;YACrD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC1C,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC5G,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACtF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mCAAmC;QACnC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,kCAAkC;QAClC,IAAI,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACzE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QACrG,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,WAAW,CAAC,KAAY,EAAE,OAAgB;QACxC,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,OAAO,GAAG,UAAU,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;IACxD,CAAC;IAED,QAAQ,CAAC,KAAY,EAAE,OAAgB;QACrC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QAEvE,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,UAAU,CAAC,OAAe,EAAE,OAAgB;QAC1C,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,OAAgB;QACvC,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC;IACnE,CAAC;IAED,UAAU,CAAC,OAAe,EAAE,OAAgB;QAC1C,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,uBAAuB,CAAC,KAAY,EAAE,WAAsB;QAC1D,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAE5B,mDAAmD;QACnD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,6FAA6F,CAAC;QAC3G,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,0FAA0F,CAAC;QACxG,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACvF,OAAO,IAAI,oGAAoG,CAAC;QAClH,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACpF,OAAO,IAAI,qEAAqE,CAAC;QACnF,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,gFAAgF,CAAC;QAC9F,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACnF,OAAO,IAAI,wDAAwD,CAAC;QACtE,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3G,OAAO,IAAI,sEAAsE,CAAC;QACpF,CAAC;QAED,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,qBAAqB,CAAC;YACjC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gBACxC,OAAO,IAAI,QAAQ,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE,CAAC;YAChD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACzC,aAAa,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAChC,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACpC,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;CACF"}