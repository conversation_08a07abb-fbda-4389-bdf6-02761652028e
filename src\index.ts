#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import figlet from 'figlet';
import { TerminalInterface } from './terminal/TerminalInterface.js';
import { ConfigManager } from './config/ConfigManager.js';
import { OnboardingComponent } from './terminal/components/OnboardingComponent.js';
import { HeaderComponent } from './terminal/components/HeaderComponent.js';
import { StatusIndicator } from './terminal/components/ThinkingAnimation.js';
import { ErrorHandler } from './utils/ErrorHandler.js';

const program = new Command();
const errorHandler = new ErrorHandler();

// Package information
const packageInfo = {
  name: 'arien-ai-cli',
  version: '1.0.0',
  description: 'Modern and powerful CLI terminal system with LLM integration'
};

program
  .name('arien')
  .description(packageInfo.description)
  .version(packageInfo.version);

// Main interactive command
program
  .command('chat', { isDefault: true })
  .description('Start interactive chat mode')
  .option('-p, --provider <provider>', 'LLM provider to use (deepseek/ollama)')
  .option('-m, --model <model>', 'Model to use')
  .option('--auto-approve', 'Auto-approve shell commands (use with caution)')
  .action(async (_options) => {
    try {
      const terminal = new TerminalInterface();
      
      // Handle graceful shutdown
      process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n⚠️  Shutting down gracefully...'));
        await terminal.stop();
        process.exit(0);
      });

      process.on('SIGTERM', async () => {
        console.log(chalk.yellow('\n⚠️  Received SIGTERM, shutting down...'));
        await terminal.stop();
        process.exit(0);
      });

      await terminal.start();
    } catch (error) {
      errorHandler.logError(error as Error, 'Application startup');
      process.exit(1);
    }
  });

// Setup command
program
  .command('setup')
  .description('Run the setup wizard to configure providers')
  .action(async () => {
    try {
      const configManager = new ConfigManager();
      await configManager.loadConfig();
      
      const onboarding = new OnboardingComponent(configManager);
      await onboarding.start();
      
      StatusIndicator.success('Setup completed successfully!');
    } catch (error) {
      errorHandler.logError(error as Error, 'Setup');
      process.exit(1);
    }
  });

// Config commands
const configCmd = program
  .command('config')
  .description('Configuration management');

configCmd
  .command('show')
  .description('Show current configuration')
  .action(async () => {
    try {
      const configManager = new ConfigManager();
      const config = await configManager.loadConfig();
      
      console.log(chalk.cyan('📋 Current Configuration:\n'));
      console.log(chalk.yellow('Provider:'), config.currentProvider);
      console.log(chalk.yellow('Model:'), config.currentModel);
      console.log(chalk.yellow('Working Directory:'), config.workingDirectory);
      console.log(chalk.yellow('Auto-approve:'), config.autoApprove ? 'enabled' : 'disabled');
      console.log(chalk.yellow('Max Retries:'), config.maxRetries);
      console.log(chalk.yellow('Config File:'), configManager.getConfigPath());
      
      console.log(chalk.cyan('\n🔧 Configured Providers:'));
      Object.entries(config.providers).forEach(([name, provider]) => {
        console.log(chalk.green(`  ${name}:`));
        console.log(chalk.gray(`    Models: ${provider.models.join(', ')}`));
        if ('baseUrl' in provider) {
          console.log(chalk.gray(`    Base URL: ${provider.baseUrl}`));
        }
      });
    } catch (error) {
      errorHandler.logError(error as Error, 'Config show');
      process.exit(1);
    }
  });

configCmd
  .command('reset')
  .description('Reset configuration to defaults')
  .option('--force', 'Force reset without confirmation')
  .action(async (options) => {
    try {
      if (!options.force) {
        const { confirm } = await import('inquirer').then(m => m.default.prompt([
          {
            type: 'confirm',
            name: 'confirm',
            message: 'Are you sure you want to reset the configuration?',
            default: false
          }
        ]));
        
        if (!confirm) {
          console.log(chalk.yellow('Configuration reset cancelled.'));
          return;
        }
      }
      
      const configManager = new ConfigManager();
      const fs = await import('fs-extra');
      
      if (await fs.pathExists(configManager.getConfigPath())) {
        await fs.remove(configManager.getConfigPath());
        StatusIndicator.success('Configuration reset successfully!');
      } else {
        StatusIndicator.info('No configuration file found.');
      }
    } catch (error) {
      errorHandler.logError(error as Error, 'Config reset');
      process.exit(1);
    }
  });

// System info command
program
  .command('info')
  .description('Show system information')
  .action(() => {
    console.log(HeaderComponent.renderBanner());
    console.log();
    console.log(HeaderComponent.renderSystemInfo());
  });

// Version command with banner
program
  .command('version')
  .description('Show version information')
  .action(() => {
    const title = figlet.textSync('Arien AI', {
      font: 'Small',
      horizontalLayout: 'default'
    });
    
    console.log(chalk.cyan(title));
    console.log(chalk.gray(`Version: ${packageInfo.version}`));
    console.log(chalk.gray(`Description: ${packageInfo.description}`));
    console.log(chalk.gray(`Node.js: ${process.version}`));
  });

// Help command override
program
  .command('help')
  .description('Show help information')
  .action(() => {
    console.log(HeaderComponent.renderBanner());
    console.log();
    console.log(HeaderComponent.renderHelp());
    console.log();
    console.log(chalk.cyan('📚 CLI Commands:'));
    console.log(chalk.yellow('  arien chat        ') + chalk.gray('Start interactive chat mode (default)'));
    console.log(chalk.yellow('  arien setup       ') + chalk.gray('Run setup wizard'));
    console.log(chalk.yellow('  arien config show ') + chalk.gray('Show current configuration'));
    console.log(chalk.yellow('  arien config reset') + chalk.gray('Reset configuration'));
    console.log(chalk.yellow('  arien info        ') + chalk.gray('Show system information'));
    console.log(chalk.yellow('  arien version     ') + chalk.gray('Show version information'));
    console.log(chalk.yellow('  arien help        ') + chalk.gray('Show this help'));
  });

// Error handling for unknown commands
program.on('command:*', () => {
  console.error(chalk.red(`❌ Unknown command: ${program.args.join(' ')}`));
  console.log(chalk.gray('Run "arien help" for available commands.'));
  process.exit(1);
});

// Parse command line arguments
async function main() {
  try {
    // If no arguments provided, show help
    if (process.argv.length === 2) {
      const terminal = new TerminalInterface();
      await terminal.start();
      return;
    }
    
    await program.parseAsync(process.argv);
  } catch (error) {
    errorHandler.logError(error as Error, 'CLI');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, _promise) => {
  console.error(chalk.red('❌ Unhandled Promise Rejection:'), reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ Uncaught Exception:'), error.message);
  process.exit(1);
});

// Start the application
main().catch((error) => {
  errorHandler.logError(error, 'Main');
  process.exit(1);
});
