import inquirer from 'inquirer';
import chalk from 'chalk';
import figlet from 'figlet';
import gradient from 'gradient-string';
import boxen from 'boxen';
import { ConfigManager } from '../../config/ConfigManager.js';
import { DeepseekProvider } from '../../providers/DeepseekProvider.js';
import { OllamaProvider } from '../../providers/OllamaProvider.js';
import { DeepseekConfig, OllamaConfig } from '../../types/index.js';
import { StatusIndicator, ProgressAnimation } from './ThinkingAnimation.js';

export class OnboardingComponent {
  private configManager: ConfigManager;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
  }

  async start(): Promise<void> {
    console.clear();
    this.displayWelcome();
    
    const config = this.configManager.getConfig();
    
    // Check if already configured
    if (config.providers.deepseek || config.providers.ollama) {
      const { continueSetup } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'continueSetup',
          message: 'Configuration already exists. Do you want to reconfigure?',
          default: false
        }
      ]);
      
      if (!continueSetup) {
        return;
      }
    }

    await this.setupProviders();
    StatusIndicator.success('Setup completed! Starting interactive mode...');
    
    // Small delay before starting interactive mode
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private displayWelcome(): void {
    const title = figlet.textSync('Arien AI CLI', {
      font: 'ANSI Shadow',
      horizontalLayout: 'default',
      verticalLayout: 'default'
    });

    console.log(gradient.rainbow(title));
    console.log();
    
    const welcomeBox = boxen(
      chalk.white.bold('Welcome to Arien AI CLI!\n\n') +
      chalk.gray('A modern and powerful CLI terminal system with LLM integration\n') +
      chalk.gray('for function calling and shell command execution.\n\n') +
      chalk.cyan('Features:\n') +
      chalk.white('• Execute shell commands with AI assistance\n') +
      chalk.white('• Support for Deepseek and Ollama providers\n') +
      chalk.white('• Intelligent function calling and tool usage\n') +
      chalk.white('• Real-time command execution and output processing\n') +
      chalk.white('• Smart error handling and retry logic'),
      {
        padding: 1,
        margin: 1,
        borderStyle: 'round',
        borderColor: 'cyan',
        backgroundColor: '#1a1a1a'
      }
    );

    console.log(welcomeBox);
  }

  private async setupProviders(): Promise<void> {
    const { providers } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'providers',
        message: 'Which LLM providers would you like to configure?',
        choices: [
          {
            name: 'Deepseek (API-based, requires API key)',
            value: 'deepseek',
            checked: true
          },
          {
            name: 'Ollama (Local, requires Ollama installation)',
            value: 'ollama',
            checked: false
          }
        ],
        validate: (input) => {
          if (input.length === 0) {
            return 'Please select at least one provider';
          }
          return true;
        }
      }
    ]);

    for (const provider of providers) {
      if (provider === 'deepseek') {
        await this.setupDeepseek();
      } else if (provider === 'ollama') {
        await this.setupOllama();
      }
    }

    // Set default provider
    const { defaultProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'defaultProvider',
        message: 'Which provider would you like to use as default?',
        choices: providers.map((p: string) => ({
          name: p.charAt(0).toUpperCase() + p.slice(1),
          value: p
        }))
      }
    ]);

    await this.configManager.updateCurrentProvider(defaultProvider);
  }

  private async setupDeepseek(): Promise<void> {
    console.log(chalk.cyan('\n🔧 Setting up Deepseek provider...\n'));
    
    const deepseekInfo = boxen(
      chalk.white('Deepseek Setup Information:\n\n') +
      chalk.gray('• Visit: https://platform.deepseek.com/\n') +
      chalk.gray('• Create an account and get your API key\n') +
      chalk.gray('• Available models: deepseek-chat, deepseek-reasoner\n') +
      chalk.gray('• API endpoint: https://api.deepseek.com'),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: 'blue'
      }
    );

    console.log(deepseekInfo);

    const { apiKey, customEndpoint } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: 'Enter your Deepseek API key:',
        mask: '*',
        validate: (input) => {
          if (!input || input.trim().length === 0) {
            return 'API key is required';
          }
          return true;
        }
      },
      {
        type: 'confirm',
        name: 'useCustomEndpoint',
        message: 'Do you want to use a custom API endpoint?',
        default: false
      },
      {
        type: 'input',
        name: 'customEndpoint',
        message: 'Enter custom API endpoint:',
        default: 'https://api.deepseek.com',
        when: (answers) => answers.useCustomEndpoint,
        validate: (input) => {
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      }
    ]);

    const config: DeepseekConfig = {
      name: 'deepseek',
      models: ['deepseek-chat', 'deepseek-reasoner'],
      apiKey: apiKey.trim(),
      baseUrl: customEndpoint || 'https://api.deepseek.com'
    };

    // Test connection
    const progress = new ProgressAnimation();
    progress.start('Testing Deepseek connection');

    try {
      const provider = new DeepseekProvider(config);
      const isValid = await provider.validateConnection();
      
      if (isValid) {
        // Try to get available models
        const models = await provider.getAvailableModels();
        if (models.length > 0) {
          config.models = models;
        }
        
        progress.stop();
        StatusIndicator.success('Deepseek connection successful!');
        
        await this.configManager.updateProvider('deepseek', config);
      } else {
        progress.stop();
        StatusIndicator.error('Failed to connect to Deepseek API. Please check your API key.');
        return;
      }
    } catch (error) {
      progress.stop();
      StatusIndicator.error(`Deepseek setup failed: ${(error as Error).message}`);
      return;
    }
  }

  private async setupOllama(): Promise<void> {
    console.log(chalk.cyan('\n🔧 Setting up Ollama provider...\n'));
    
    const ollamaInfo = boxen(
      chalk.white('Ollama Setup Information:\n\n') +
      chalk.gray('• Install Ollama from: https://ollama.ai/\n') +
      chalk.gray('• Start Ollama service: ollama serve\n') +
      chalk.gray('• Default endpoint: http://localhost:11434\n') +
      chalk.gray('• Pull models: ollama pull llama2'),
      {
        padding: 1,
        borderStyle: 'round',
        borderColor: 'green'
      }
    );

    console.log(ollamaInfo);

    const { customEndpoint } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'useCustomEndpoint',
        message: 'Do you want to use a custom Ollama endpoint?',
        default: false
      },
      {
        type: 'input',
        name: 'customEndpoint',
        message: 'Enter Ollama endpoint:',
        default: 'http://localhost:11434',
        when: (answers) => answers.useCustomEndpoint,
        validate: (input) => {
          try {
            new URL(input);
            return true;
          } catch {
            return 'Please enter a valid URL';
          }
        }
      }
    ]);

    const baseUrl = customEndpoint || 'http://localhost:11434';

    const config: OllamaConfig = {
      name: 'ollama',
      models: [],
      baseUrl
    };

    // Test connection and get models
    const progress = new ProgressAnimation();
    progress.start('Testing Ollama connection');

    try {
      const provider = new OllamaProvider(config);
      const isValid = await provider.validateConnection();
      
      if (isValid) {
        const models = await provider.getAvailableModels();
        progress.stop();
        
        if (models.length === 0) {
          StatusIndicator.warning('No models found in Ollama. You may need to pull some models first.');
          console.log(chalk.gray('Example: ollama pull llama2'));
          config.models = ['llama2']; // Default fallback
        } else {
          StatusIndicator.success(`Ollama connection successful! Found ${models.length} models.`);
          config.models = models;
        }
        
        await this.configManager.updateProvider('ollama', config);
      } else {
        progress.stop();
        StatusIndicator.error('Failed to connect to Ollama. Make sure Ollama is running.');
        return;
      }
    } catch (error) {
      progress.stop();
      StatusIndicator.error(`Ollama setup failed: ${(error as Error).message}`);
      return;
    }
  }
}
