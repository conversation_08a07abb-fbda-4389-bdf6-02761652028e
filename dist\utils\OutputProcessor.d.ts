import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatMessage } from '../types/index.js';
export interface ProcessingOptions {
    maxLength?: number;
    highlightKeywords?: string[];
    formatJson?: boolean;
    showLineNumbers?: boolean;
    truncateLines?: number;
    colorScheme?: 'default' | 'minimal' | 'verbose';
    showToolDetails?: boolean;
    showToolExecutionDetails?: boolean;
}
export interface ProcessedOutput {
    formatted: string;
    summary: string;
    metadata: {
        lineCount: number;
        characterCount: number;
        hasErrors: boolean;
        detectedFormat: string;
        keywords: string[];
    };
}
export declare class OutputProcessor {
    private static readonly DEFAULT_OPTIONS;
    static processShellOutput(result: ShellResult, options?: ProcessingOptions): ProcessedOutput;
    static processToolOutput(result: ToolResult, options?: ProcessingOptions): ProcessedOutput;
    static processAssistantMessage(message: ChatMessage, options?: ProcessingOptions): ProcessedOutput;
    private static processText;
    private static applySyntaxHighlighting;
    private static highlightJson;
    private static addLineNumbers;
    private static highlightKeywords;
    private static applyColorScheme;
    private static formatExecutionMetadata;
    private static isJson;
    private static detectFormat;
    private static containsErrors;
    private static extractKeywords;
    private static generateShellSummary;
    private static generateToolSummary;
    private static generateMessageSummary;
    private static generateTextSummary;
    static stripColors(text: string): string;
    static truncateText(text: string, maxLength: number): string;
    static formatFileSize(bytes: number): string;
}
//# sourceMappingURL=OutputProcessor.d.ts.map