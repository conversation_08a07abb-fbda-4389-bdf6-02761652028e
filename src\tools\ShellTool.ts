import { spawn, SpawnOptions } from 'child_process';
import os from 'os';
import { ShellCommand, ShellResult, ToolDefinition } from '../types/index.js';
import { PlatformUtils } from '../utils/PlatformUtils.js';

export class ShellTool {
  private dangerousCommands: string[] = [
    'rm -rf',
    'del /f /s /q',
    'format',
    'fdisk',
    'mkfs',
    'dd if=',
    'shutdown',
    'reboot',
    'halt',
    'poweroff',
    'init 0',
    'init 6',
    'systemctl poweroff',
    'systemctl reboot'
  ];

  constructor() {
    // Constructor intentionally empty
  }

  static getToolDefinition(): ToolDefinition {
    return {
      name: 'execute_shell_command',
      description: `Execute shell commands and capture their output. This tool can run any command available in the system shell.
      
IMPORTANT SAFETY GUIDELINES:
- Always explain what the command will do before executing
- Ask for user confirmation for potentially destructive operations
- Use appropriate working directories
- Handle errors gracefully and provide meaningful feedback
- Respect system permissions and security boundaries

WHEN TO USE:
- File system operations (ls, dir, find, locate)
- Package management (npm, pip, apt, brew, etc.)
- Git operations (status, add, commit, push, pull)
- Build and compilation tasks
- System information gathering
- Text processing and file manipulation
- Network operations (ping, curl, wget)
- Process management (ps, kill, top)

WHEN NOT TO USE:
- Destructive operations without explicit user consent
- Commands that require interactive input (use non-interactive flags)
- Long-running services (prefer background execution)
- Commands that modify system-critical files
- Operations that could compromise security

PARALLEL vs SEQUENTIAL EXECUTION:
- PARALLEL: Use for independent operations like multiple file downloads, parallel builds, or concurrent status checks
- SEQUENTIAL: Use for dependent operations like build->test->deploy, or when order matters

EXAMPLES:
- File listing: ls -la / dir
- Git status: git status --porcelain
- Package install: npm install package-name
- Build project: npm run build
- System info: uname -a / systeminfo
- Find files: find . -name "*.js" / dir /s *.js`,
      parameters: {
        type: 'object',
        properties: {
          command: {
            type: 'string',
            description: 'The shell command to execute (without shell operators like && or ||)'
          },
          args: {
            type: 'array',
            description: 'Optional array of command arguments (alternative to including them in command string)'
          },
          cwd: {
            type: 'string',
            description: 'Working directory for command execution (defaults to current directory)'
          },
          timeout: {
            type: 'number',
            description: 'Timeout in milliseconds (default: 30000ms)'
          },
          requireApproval: {
            type: 'boolean',
            description: 'Whether to require user approval before execution (default: auto-detect based on command)'
          }
        },
        required: ['command']
      },
      usage: {
        when_to_use: 'For executing system commands, file operations, package management, git operations, builds, and system information gathering',
        when_not_to_use: 'For destructive operations without consent, interactive commands, or long-running services',
        parallel_execution: true,
        sequential_execution: true,
        examples: [
          'ls -la (list directory contents)',
          'git status (check git repository status)',
          'npm install (install dependencies)',
          'find . -name "*.ts" (find TypeScript files)',
          'ps aux | grep node (find Node.js processes)'
        ]
      }
    };
  }

  async execute(command: ShellCommand): Promise<ShellResult> {
    const startTime = Date.now();

    try {
      // Translate generic commands to platform-specific ones
      const translatedCommand = this.translateCommand(command.command);
      const commandToExecute = { ...command, command: translatedCommand };

      // Validate and prepare command
      const { cmd, args, options } = this.prepareCommand(commandToExecute);

      // Check for dangerous commands
      if (this.isDangerousCommand(cmd, args)) {
        throw new Error(`Dangerous command detected: ${cmd} ${args.join(' ')}. This command requires explicit user approval.`);
      }

      // Execute command with timeout
      const result = await this.executeCommand(cmd, args, options, command.timeout || 30000);

      const executionTime = Date.now() - startTime;

      return {
        success: result.exitCode === 0,
        stdout: result.stdout,
        stderr: result.stderr,
        exitCode: result.exitCode,
        executionTime,
        command: `${cmd} ${args.join(' ')}`
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      return {
        success: false,
        stdout: '',
        stderr: (error as Error).message,
        exitCode: -1,
        executionTime,
        command: command.command
      };
    }
  }

  private translateCommand(commandString: string): string {
    // Parse the command to extract the main command and arguments
    const parts = this.parseCommand(commandString);
    if (parts.length === 0) {
      return commandString;
    }

    const mainCommand = parts[0];
    if (!mainCommand) {
      return commandString;
    }

    const args = parts.slice(1);

    // Try to translate the command
    const translated = PlatformUtils.translateCommand(mainCommand, args);

    // If translation returned the same command, it means no translation was needed
    if (translated === mainCommand) {
      return commandString;
    }

    return translated;
  }

  private prepareCommand(command: ShellCommand): { cmd: string; args: string[]; options: SpawnOptions } {
    let cmd: string;
    let args: string[];
    
    if (command.args && command.args.length > 0) {
      cmd = command.command;
      args = command.args;
    } else {
      // Parse command string into command and arguments
      const parts = this.parseCommand(command.command);
      cmd = parts[0] || '';
      args = parts.slice(1);
    }
    
    // Determine shell and adjust command for cross-platform compatibility
    const isWindows = os.platform() === 'win32';
    
    if (isWindows) {
      // On Windows, use cmd.exe to execute commands
      args = ['/c', cmd, ...args];
      cmd = 'cmd.exe';
    }
    
    const options: SpawnOptions = {
      cwd: command.cwd || process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: !isWindows, // Use shell on Unix-like systems
      env: { ...process.env }
    };
    
    return { cmd, args, options };
  }

  private parseCommand(commandString: string): string[] {
    // Simple command parsing - splits on spaces but respects quotes
    const parts: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    
    for (let i = 0; i < commandString.length; i++) {
      const char = commandString[i];
      
      if ((char === '"' || char === "'") && !inQuotes) {
        inQuotes = true;
        quoteChar = char;
      } else if (char === quoteChar && inQuotes) {
        inQuotes = false;
        quoteChar = '';
      } else if (char === ' ' && !inQuotes) {
        if (current.trim()) {
          parts.push(current.trim());
          current = '';
        }
      } else {
        current += char;
      }
    }
    
    if (current.trim()) {
      parts.push(current.trim());
    }
    
    return parts;
  }

  private isDangerousCommand(cmd: string, args: string[]): boolean {
    const fullCommand = `${cmd} ${args.join(' ')}`.toLowerCase();
    
    return this.dangerousCommands.some(dangerous => 
      fullCommand.includes(dangerous.toLowerCase())
    );
  }

  private executeCommand(cmd: string, args: string[], options: SpawnOptions, timeout: number): Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
  }> {
    return new Promise((resolve, reject) => {
      const child = spawn(cmd, args, options);
      
      let stdout = '';
      let stderr = '';
      let timeoutId: NodeJS.Timeout;
      
      // Set up timeout
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          child.kill('SIGTERM');
          reject(new Error(`Command timed out after ${timeout}ms`));
        }, timeout);
      }
      
      // Collect stdout
      if (child.stdout) {
        child.stdout.on('data', (data) => {
          stdout += data.toString();
        });
      }
      
      // Collect stderr
      if (child.stderr) {
        child.stderr.on('data', (data) => {
          stderr += data.toString();
        });
      }
      
      // Handle process completion
      child.on('close', (code) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        resolve({
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code || 0
        });
      });
      
      // Handle process errors
      child.on('error', (error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        reject(error);
      });
    });
  }

  validateCommand(command: string): { valid: boolean; reason?: string } {
    if (!command || command.trim().length === 0) {
      return { valid: false, reason: 'Command cannot be empty' };
    }

    // Allow common shell operators for legitimate use cases
    const dangerousPatterns = [
      /rm\s+-rf\s+\/[^\/\s]*/,  // rm -rf on root directories
      /del\s+\/[fFsS]\s+\/[qQ]/,  // Windows delete with force and quiet
      /format\s+[cC]:/,  // Format C drive
      />\s*\/dev\/sd[a-z]/,  // Write to disk devices
      /dd\s+if=/,  // Disk dump operations
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(command)) {
        return {
          valid: false,
          reason: 'Command contains potentially destructive operations. Please use with caution.'
        };
      }
    }

    return { valid: true };
  }

  async executeWithApproval(command: ShellCommand, autoApprove: boolean = false): Promise<ShellResult> {
    const validation = this.validateCommand(command.command);
    if (!validation.valid) {
      throw new Error(validation.reason);
    }

    const requiresApproval = command.requireApproval ||
                           this.isDangerousCommand(command.command, command.args || []) ||
                           !autoApprove;

    if (requiresApproval && !autoApprove) {
      // This would be handled by the terminal interface
      throw new Error('APPROVAL_REQUIRED');
    }

    return this.execute(command);
  }
}
